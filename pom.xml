<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.sub</groupId>
    <artifactId>subm</artifactId>
    <version>1.0</version>
    <name>sub-subm</name>
    <description>SubM Project 统一依赖管理</description>
    <modules>
        <module>subm-api</module>
        <module>subm-dal</module>
        <module>common</module>
        <module>framework</module>
    </modules>
    <packaging>pom</packaging>

    <properties>
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <pagehelper.boot.version>2.1.0</pagehelper.boot.version>
        <spring-security.version>6.4.5</spring-security.version>
        <spring-boot.version>3.4.5</spring-boot.version>
        <langchain4j.version>1.1.0-beta7</langchain4j.version>
        <dynamic.version>4.3.1</dynamic.version>
        <druid.version>1.2.20</druid.version>
        <mybatisplus.version>3.5.5</mybatisplus.version>
        <jasypt.version>3.0.5</jasypt.version>
        <fastjson.version>2.0.43</fastjson.version>
        <faauth.version>1.3</faauth.version>
        <jjwt.version>0.12.6</jjwt.version>
        <jaxb.version>2.3.0</jaxb.version>
        <mysql.version>8.0.28</mysql.version>
        <ip.version>2.7.0</ip.version>
        <junrar.version>7.5.5</junrar.version>
        <common.io.version>2.16.1</common.io.version>
        <rediss.version>3.46.0</rediss.version>
        <kntife.version>4.4.0</kntife.version>
        <hutool.version>5.8.38</hutool.version>
        <oshi.version>6.6.2</oshi.version>
        <poi.version>4.1.2</poi.version>
        <lombok.version>1.18.34</lombok.version>
        <ssh.version>0.1.55</ssh.version>
        <bitwalker.version>1.21</bitwalker.version>
        <ipregion.vesrion>2.7.0</ipregion.vesrion>
        <jsoup.version>1.18.1</jsoup.version>
        <selenium.version>3.14.0</selenium.version>
        <tencentapi.version>3.1.830</tencentapi.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <flexmark.version>0.64.8</flexmark.version>
        <jcraft.version>2.27.0</jcraft.version>
        <springdoc.version>2.8.8</springdoc.version>
        <dom4j.version>2.1.1</dom4j.version>
        <rome.version>2.1.0</rome.version>
        <batik.version>1.19</batik.version>
    </properties>
    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>
             <!-- langchain -->
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-spring-boot-starter</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <!-- langchain openai -->
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-open-ai-spring-boot-starter</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <!-- spring boot 主类 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId>
                <version>${ssh.version}</version>
            </dependency>
            <!-- 获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>
            <!-- 配置文件加密 -->
            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>${jasypt.version}</version>
            </dependency>
            <!-- ip地址解析  -->
            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${common.io.version}</version>
            </dependency>
            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>
            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>
            <!-- 2FA -->
            <dependency>
                <groupId>com.j256.two-factor-auth</groupId>
                <artifactId>two-factor-auth</artifactId>
                <version>${faauth.version}</version>
            </dependency>
            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!--token-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb.version}</version>
            </dependency>
            <!--Mysql 驱动-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <!-- mybatis plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatisplus.version}</version>
            </dependency>
            <!-- 多数据源连接池 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${dynamic.version}</version>
            </dependency>
            <!--  数据池datasource pool-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!-- api文档 -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${kntife.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <!--  工具库-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <!-- IP2region Java 组件使应用程序能够从 IP 地址获取信息 -->
            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ipregion.vesrion}</version>
            </dependency>

            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>

            <dependency>
                <groupId>com.rometools</groupId>
                <artifactId>rome</artifactId>
                <version>${rome.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.xmlgraphics</groupId>
                <artifactId>batik-transcoder</artifactId>
                <version>${batik.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.xmlgraphics</groupId>
                <artifactId>batik-codec</artifactId>
                <version>${batik.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${rediss.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>17</source>
                        <target>17</target>
                        <parameters>true</parameters>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.1.8</version>
            </plugin>
        </plugins>
    </build>

</project>

FROM alpine

ENV SPRING_HOME=/spring
ENV DEPLOY_DIR=/spring/server

WORKDIR $SPRING_HOME
COPY . $SPRING_HOME/target

EXPOSE 8999 8000

RUN  mkdir -p $DEPLOY_DIR \
    && mv ./target/distribution/subm-api-1.0/* ./server \
    && mv ./target/Dockerfile ./ \
    && rm -rf target  \
    && apk update && apk add openjdk17-jre && apk add bash && apk add fontconfig

ENTRYPOINT ["bash", "-c", "/spring/server/bin/server.sh start-debug"]

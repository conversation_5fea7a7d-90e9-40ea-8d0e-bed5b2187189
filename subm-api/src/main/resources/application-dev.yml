spring:
  application:
    # 服务名称, 用于区分程序调用的是哪一个服务(分布式环境下建议每个名称不同)
    name: subm-api
  data:
    redis:
      database: 5
      host: 127.0.0.1
      port: 6379
      connect-timeout: 60000
      lettuce:
        pool:
          max-active: 20
          max-wait: -1
          max-idle: 5
          min-idle: 0
  datasource:
    dynamic:
      # 设置默认的数据源或者数据源组，默认值即为 master
      primary: master
      # 严格模式，默认false. 设置为true后在未匹配到指定数据源时候会抛出异常
      strict: false
      datasource:
        master:
          url: *********************************************************************************************************************************
          username: root
          password: zhyzhy356789
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
    druid:
      db-type: com.alibaba.druid.pool.DruidDataSource
      driver: com.mysql.cj.jdbc.Driver
      # 初始连接数
      initial-size: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000

      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      # 申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      testOnBorrow: false
      # 归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      testOnReturn: false
      # 申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效
      testWhileIdle: true
      # 配置连接在池中的最小生存时间，单位是毫秒（改为30秒，避免连接超时）
      minEvictableIdleTimeMillis: 30000
      # 配置连接在池中的最大生存时间，单位是毫秒（改为60秒）
      maxEvictableIdleTimeMillis: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒（改为30秒）
      timeBetweenEvictionRunsMillis: 30000
      # 配置一个连接池中最多的空闲连接数，超过的空闲连接将被释放，如果设置为负数表示不限制
      maxPoolPreparedStatementPerConnectionSize: 20
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

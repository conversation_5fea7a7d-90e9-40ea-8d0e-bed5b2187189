#项目相关配置
project:
  # 名称
  name: subm-api
  # 版本
  version: 1.0
  # 版本年份
  copyrightYear: 2024
  # 文件路径 示例（ Windows配置D:/extend/uploadPath，Linux配置 /home/<USER>/extend/uploadPath）
  profile: C:\\Users\\<USER>\\Desktop\\weight\\feeds\\

#多线程数设置
task:
  # 核心线程数
  corePoolSize: 10
  # 最大线程数
  maxPoolSize: 10
  # 队列数
  queueCapacity: 200
  # 最大空闲时间
  keepAliveSeconds: 300

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8888
  port: 8900

# spring 配置
spring:
  profiles:
    # 读取那个配置文件: 如: application-dev.yml
    active: dev
  # 国际化资源文件路径
  messages:
    basename: i18n/messages
    # 编码格式
    encoding: UTF-8
    # 缓存时间（秒），-1表示永久缓存
    cache-duration: 3600
    # 当找不到消息时是否使用消息代码作为默认消息
    use-code-as-default-message: true

# knife4j的增强配置，不需要增强可以不配
knife4j:
  enable: true
  setting:
    language: zh_cn
    enable-dynamic-parameter: true
  basic:
    enable: true
    username: subfeeds
    password: subfeeds
  openapi:
    title: SubFeed API 文档
    description: 实现SubFeed  阅读器 功能 的 API文档
    licenseUrl: http://{ip/domain}:8999
    concat: jack
    url: https://github.com/jackmack56
    version: v1.0.0
    terms-of-service-url: https://github.com/jackmack56
    group:
      api-v1:
        group-name: API-V1
        api-rule: package
        api-rule-resources:
          - com.sub.subm.api.controller.v1
        path-rule: REGEX

# sql 日志输出
logging:
  level:
    com.sub: debug

# 重要配置信息加密处理, 避免数据泄露,   加密需要请调用 test 模块下 configFileDataEnc 方法:
# 加密格式如下:  ENC(加密后的数据)
jasypt:
  encryptor:
    password: jackPayment #密钥, 一般只更改设置这个
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    algorithm: PBEWithMD5AndDES
    property:
      prefix: ENC(
      suffix: )

# mybatis 处理
mybatis:
  configuration:
    #log-impl:  org.apache.ibatis.logging.nologging.NoLoggingImpl
    map-underscore-to-camel-case: true
    call-setters-on-nulls: true
  mapper-locations: classpath:mapper/*/*.xml
  type-aliases-package: classpath:mapper
# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 配置on-token信息, 如系统中不用删除此配置, 删除项目中 token 文件
one-token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  auth-key: ENC(ONwFAJsOD7I+BsSdLmk6WGORGwFZ3Ww4)
  # 长token 时间类型 [day: 天  hour: 小时  minute: 分钟]
  long-type: date
  # 长token 时间
  long-timeout: 7
  # 长token 替换时间, 表示(num)天, 将长token的有效日期重新设置
  replace-timeout: 3
  # 是否支持 同端互斥 false 不支持 true支持
  client-share: true
  # 短token 时间 [date天 hour 小时  minute 分钟]
  short-type: date
  # 短token 时间
  short-timeout: 7

package com.sub.subm.api.security;


import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import com.sub.common.constant.CacheConstants;
import com.sub.common.constant.Constants;
import com.sub.common.core.redis.RedisCache;
import com.sub.common.exception.BaseException;
import com.sub.common.utils.ServletUtils;
import com.sub.common.utils.StringUtils;
import com.sub.common.utils.ip.NetworkUtil;
import com.sub.framework.jwt.TokenBasic;
import com.sub.framework.jwt.constant.OneTokenConfig;
import com.sub.framework.jwt.exception.LoginTokenEnum;
import com.sub.framework.jwt.exception.TokenException;
import com.sub.subm.api.entity.vo.LoginUser;

import eu.bitwalker.useragentutils.UserAgent;
import io.jsonwebtoken.Claims;
import jakarta.servlet.http.HttpServletRequest;

@Component
@Configuration
public class TokenUtil extends TokenBasic {
    private Logger log = LoggerFactory.getLogger(TokenUtil.class);

    @Autowired
    private RedisCache redisCache;

    public TokenUtil(OneTokenConfig oneTokenConfig) {
        super(oneTokenConfig);
    }
    /**
     * 获取请求时的token
     * @param request
     * @return
     */
    public LoginUser getToken(HttpServletRequest request) {
        String token = request.getHeader(getHeader());
        if (StringUtils.isNotEmpty(token) && token.startsWith(Constants.TOKEN_PREFIX)) {
            token = token.replace(Constants.TOKEN_PREFIX, "");
            try {
                Claims claims = getClaimFromToken(token);
                String client = (String) claims.get("client");
                String userId = (String) claims.get("user");
                if (StringUtils.isNotEmpty(client) && StringUtils.isNotEmpty(userId)) {
                    checkClient(client);
                    String redisKey = CacheConstants.TOKEN_LOGIN +  userId + ":" + client;
                    LoginUser loginUser = redisCache.getCacheObject(redisKey);
                    return loginUser;
                }
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                throw new TokenException(LoginTokenEnum.LOGIN_INFO_ERROR);
            }
        }
        return null;
    }

    /**
     * token 生成
     */
    public String refreshToken(String redisKey, String client, LoginUser user) {
        String refreshToken = UUID.randomUUID().toString();
        user.setToken(refreshToken);
        redisCache.setCacheObject(redisKey, user, VALIDTIME);
        return generateShortToken(client, refreshToken, user.getUserId(), "login");
    }

    /***
     * 用户 生成有效token 信息
     * 1: 同端互斥性, 如 verify 为 true 则强制挤下去
     */
    public String generateUserToken(LoginUser loginUser, boolean verify) throws BaseException {
        setUserAgent(loginUser);
        String redisKey = CacheConstants.TOKEN_LOGIN + loginUser.getUserId() + ":" + loginUser.getOs();
        LoginUser getToken = redisCache.getCacheObject(redisKey);
        if (!StringUtils.isNotNull(getToken)) {
            return refreshToken(redisKey, loginUser.getOs(), loginUser);
        } else if (getClientShare() && verify) {
            return refreshToken(redisKey, loginUser.getOs(), loginUser);
        } else {
            log.warn("服务器中已有此用户token, 用户 {}, 服务token{}", loginUser.getUserId(), getToken);
            throw new TokenException(LoginTokenEnum.LOGIN_IN_REST_LOGIN);
        }
    }

    /**
     * 用户退出登录
     **/
    public LoginTokenEnum logout(String client, String user) throws BaseException {
        try {
            redisCache.deleteObject(CacheConstants.TOKEN_LOGIN +  user  + ":" +  client);
            return LoginTokenEnum.LOGOUT_LOGIN_SUCCESS;
        } catch (Exception e) {
            log.error("用户退出登录失败!!!! redis 删除键值对失败!");
            throw new TokenException(LoginTokenEnum.LOGOUT_LOGIN_FAIL);
        }
    }

    /**
     * 设置用户代理信息
     * @param loginUser 登录信息
     */
    public void setUserAgent(LoginUser loginUser) {
        UserAgent userAgent = UserAgent.parseUserAgentString(ServletUtils.getRequest().getHeader("User-Agent"));
        String ip = NetworkUtil.getIpAddr();
        loginUser.setIpAddr(ip);
        loginUser.setBrowser(userAgent.getBrowser().getName());
        loginUser.setOs(userAgent.getOperatingSystem().getName());
    }

}

package com.sub.subm.api.service;


import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sub.common.core.common.BatchRecordPo;
import com.sub.subm.dal.dao.remind.FgRemindInfoDao;
import com.sub.subm.dal.entity.generate.remind.FgRemindInfoPo;


@Service
public class RemindInfoServiceImpl {

    @Autowired
    private FgRemindInfoDao fgRemindInfoDao;


    public BigDecimal totalAmount(FgRemindInfoPo fgRemindInfoPo) {
        BigDecimal total = fgRemindInfoDao.totalAmount(fgRemindInfoPo);
        if (total == null) {
            total = BigDecimal.ZERO;
        }
        return total.setScale(2, RoundingMode.HALF_UP);
    }


    public List<FgRemindInfoPo> filterFgRemindInfoList(FgRemindInfoPo fgRemindInfoPo) {
        return fgRemindInfoDao.filterFgRemindInfoList(fgRemindInfoPo);
    }


    public void insertFgRemindInfo(FgRemindInfoPo fgRemindInfoPo) {
        List<FgRemindInfoPo> list = new ArrayList<>();
        list.add(fgRemindInfoPo);
        fgRemindInfoDao.insertFgRemindInfo(new BatchRecordPo<>(list));
    }
}

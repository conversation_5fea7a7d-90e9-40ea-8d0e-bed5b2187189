package com.sub.subm.api.entity.vo;

import java.util.Collection;
import java.util.Set;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import com.alibaba.fastjson2.annotation.JSONField;
import com.sub.subm.dal.entity.generate.user.UserPo;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "登录用户身份权限")
public class LoginUser implements UserDetails {
    @Schema(description = "用户id")
    private String userId;
    @Schema(description = "角色id")
    private String roleId;
    @Schema(description = "用户唯一标识")
    private String token;
    @Schema(description = "登录时间")
    private Long loginTime;
    @Schema(description = "过期时间")
    private Long expireTime;
    @Schema(description = "登录IP地址")
    private String ipAddr;
    @Schema(description = "浏览器")
    private String browser;
    @Schema(description = "操作系统")
    private String os;
    @Schema(description = "权限列表")
    private Set<String> permissions;
    @Schema(description = "用户信息")
    private UserPo user;
    @Schema(description = "是否开启二项验证")
    private Integer twoEnable;

    public LoginUser(UserPo user, Set<String> permissions) {
        this.user = user;
        this.permissions = permissions;
    }

    public LoginUser() {
    }

    public LoginUser(String userId, UserPo user) {
        this.userId = userId;
        this.user = user;
        this.roleId = user.getRoleId();
    }

    public LoginUser(String userId, UserPo user, Set<String> permissions, Integer twoEnable) {
        this.userId = userId;
        this.roleId = user.getRoleId();
        this.user = user;
        this.permissions = permissions;
        this.twoEnable = twoEnable;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getIpAddr() {
        return ipAddr;
    }

    public void setIpAddr(String ipAddr) {
        this.ipAddr = ipAddr;
    }

    public Integer getTwoEnable() {
        return twoEnable;
    }

    public void setTwoEnable(Integer twoEnable) {
        this.twoEnable = twoEnable;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }


    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    @JSONField(serialize = false)
    @Override
    public String getPassword() {
        return user.getPassword();
    }

    @Override
    public String getUsername() {
        return user.getName();
    }

    /**
     * 账户是否未过期,过期无法验证
     */
    @JSONField(serialize = false)
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    /**
     * 指定用户是否解锁,锁定的用户无法进行身份验证
     *
     * @return
     */
    @JSONField(serialize = false)
    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    /**
     * 指示是否已过期的用户的凭据(密码),过期的凭据防止认证
     *
     * @return
     */
    @JSONField(serialize = false)
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    /**
     * 是否可用 ,禁用的用户不能身份验证
     *
     * @return
     */
    @JSONField(serialize = false)
    @Override
    public boolean isEnabled() {
        return true;
    }

    public Long getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(Long loginTime) {
        this.loginTime = loginTime;
    }



    public String getBrowser() {
        return browser;
    }

    public void setBrowser(String browser) {
        this.browser = browser;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public Long getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Long expireTime) {
        this.expireTime = expireTime;
    }

    public Set<String> getPermissions() {
        return permissions;
    }

    public void setPermissions(Set<String> permissions) {
        this.permissions = permissions;
    }

    public UserPo getUser() {
        return user;
    }

    public void setUser(UserPo user) {
        this.user = user;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null;
    }
}

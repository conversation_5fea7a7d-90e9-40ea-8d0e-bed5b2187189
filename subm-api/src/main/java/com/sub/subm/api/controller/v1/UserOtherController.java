package com.sub.subm.api.controller.v1;


import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.sub.common.core.common.CommonResult;
import com.sub.subm.dal.entity.generate.user.UserFeedsBackPo;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping(value = "/api/v1")
@Tag(name = "用户其他模块")
public class UserOtherController extends CommonResult {


    @RequestMapping(value = "submitFeedBack", method = RequestMethod.POST)
    @Operation(summary = "用户提交反馈")
    public CommonResult submitFeedBack(@RequestBody UserFeedsBackPo userFeedsBackPo) throws Exception {
        return CommonResult.ok();
    }
}

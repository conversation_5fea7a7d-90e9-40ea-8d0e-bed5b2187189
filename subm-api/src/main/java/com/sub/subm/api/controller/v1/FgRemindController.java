package com.sub.subm.api.controller.v1;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.sub.common.constant.CacheConstants;
import com.sub.common.core.common.CommonResult;
import com.sub.common.core.page.PageVo;
import com.sub.common.core.redis.RedisCache;
import com.sub.common.exception.BaseException;
import com.sub.common.utils.TimeUtils;
import com.sub.subm.api.entity.vo.filterRemindVO;
import com.sub.subm.api.security.SecurityUtils;
import com.sub.subm.api.service.ExchangRateService;
import com.sub.subm.api.service.PrefixJoinServiceImpl;
import com.sub.subm.api.service.RemindServiceImpl;
import com.sub.subm.api.service.UserServiceImpl;
import com.sub.subm.dal.entity.generate.remind.FgRemindInfoPo;
import com.sub.subm.dal.entity.generate.remind.FgRemindPo;
import com.sub.subm.dal.entity.generate.user.UserPo;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;


@Tag(name = "提醒相关接口")
@RestController
@RequestMapping("/api/v1/remind")
@Slf4j
public class FgRemindController extends CommonResult {

    private static final int REMIND_STAT_CACHE_TIMEOUT = 10; // 提醒统计缓存10分钟

    @Autowired
    private RemindServiceImpl remindService;
    @Autowired
    private PrefixJoinServiceImpl prefixJoinService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private UserServiceImpl userService;
    @Autowired
    private ExchangRateService exchangRateService;

    @RequestMapping(value = "filterRemind", method = RequestMethod.GET)
    @Operation(summary = "查询提醒")
    public CommonResult filterRemind() throws BaseException {
        String userId = SecurityUtils.getUserId();
        FgRemindPo fgRemindPo = new FgRemindPo();
        fgRemindPo.setUserId(userId);
        startPage();
        List<filterRemindVO> list = remindService.filterFgRemindList(fgRemindPo);
        PageVo pageVo = new PageVo(list);
        pageVo.setPageList(prefixJoinService.prefixImgRemind(list));
        return ok(pageVo);
    }

    @RequestMapping(value = "filterStat", method = RequestMethod.GET)
    @Operation(summary = "查询提醒统计")
    public CommonResult filterStat() throws BaseException {
        String userId = SecurityUtils.getUserId();
        UserPo userPo = userService.getByIdUserPo(userId);
        String cacheKey = CacheConstants.REMIND_STAT_KEY + ":" + userId;

        // 尝试从缓存获取数据
        try {
            Map<String, BigDecimal> cachedData = redisCache.getCacheObject(cacheKey);
            if (cachedData != null) {
                log.debug("从缓存获取提醒统计数据, userId: {}, key: {}", userId, cacheKey);
                return ok(cachedData);
            }
        } catch (Exception e) {
            log.warn("获取提醒统计缓存失败, userId: {}, error: {}", userId, e.getMessage());
        }

        // 缓存未命中，执行原有业务逻辑
        FgRemindInfoPo fgRemindInfoPo = new FgRemindInfoPo();
        fgRemindInfoPo.setUserId(userId);
        Map<String, Object> map = new HashMap<>();        

        fgRemindInfoPo.setEndTime(TimeUtils.getCurrentTime());


        fgRemindInfoPo.setStartTime(TimeUtils.getMonthStartTime());
        map.put("month", remindService.totalAmount(fgRemindInfoPo));

        fgRemindInfoPo.setStartTime(TimeUtils.getYearStartTime());
        map.put("year", remindService.totalAmount(fgRemindInfoPo));

        String currency = userPo.getCurrency();
        String currencySymbol = exchangRateService.getCurrencySymbol(currency);
        map.put("currency", currency);
        map.put("currencySymbol", currencySymbol);
        

        
        // 将结果存储到缓存
        try {
            redisCache.setCacheObject(cacheKey, map, REMIND_STAT_CACHE_TIMEOUT, java.util.concurrent.TimeUnit.MINUTES);
            log.debug("提醒统计数据已缓存, userId: {}, key: {}", userId, cacheKey);
        } catch (Exception e) {
            log.warn("缓存提醒统计数据失败, userId: {}, error: {}", userId, e.getMessage());
        }

        return ok(map);
    }

    @RequestMapping(value = "updateRemind", method = RequestMethod.POST)
    @Operation(summary = "更新提醒")
    public CommonResult updateRemind(@RequestBody FgRemindPo fgRemindPo) throws BaseException {
        fgRemindPo.setUserId(SecurityUtils.getUserId());
        remindService.updateFgRemindPo(fgRemindPo);
        return ok();
    }

    @RequestMapping(value = "addRemind", method = RequestMethod.POST)
    @Operation(summary = "添加提醒")
    public CommonResult addRemind(@RequestBody FgRemindPo fgRemindPo) throws BaseException {
    
        fgRemindPo.setUserId(SecurityUtils.getUserId());
        fgRemindPo.setStatus(1);
        fgRemindPo.setCreateTime(fgRemindPo.getStartTime());
        remindService.insertFgRemindPo(fgRemindPo); 
        return ok();
    }

    @RequestMapping(value = "deleteRemind", method = RequestMethod.DELETE)
    @Operation(summary = "删除提醒")
    public CommonResult deleteRemind(@RequestParam("arrayId") List<String> arrayId) throws BaseException {
        String userId = SecurityUtils.getUserId();
        if (arrayId == null && arrayId.size() == 0) {
            throw new BaseException(-400, "invalid.handle");
        }
        remindService.deleteFgRemindPo(arrayId, userId);
        return ok();
    }

    /**
     * 根据日期获取需要进行提醒的记录列表
     * 年提醒：判断这一天是否和开始日期的开始日期一样
     * 月提醒：判断这一天是否和开始日期的开始日期一样
     * 
     */
    @RequestMapping(value = "getRemindByDay",method = RequestMethod.GET)
    public CommonResult getRemindByMonth(@RequestParam("time") Long time) throws BaseException {
        String userId = SecurityUtils.getUserId();
        List<FgRemindPo> list = remindService.getRemindByDay(userId, time);
        return ok(list);
    }

    /**
     * 根据月份获取需要进行提醒的记录列表
     * 年提醒：判断这个月是不是和开始日期同一个月
     * 周提醒：这个月都要展示
     * 日提醒：这个月都要展示
     * 永久提醒：这个月都要展示
     */
    @RequestMapping(value = "getRemindByMonth",method = RequestMethod.GET)
    @Operation(summary = "根据月份获取提醒记录列表")
    public CommonResult getRemindByMonthNew(@RequestParam("time") Long time) throws BaseException {
        String userId = SecurityUtils.getUserId();
        List<FgRemindPo> list = remindService.getRemindByMonth(userId, time);
        return ok(list);
    }


}

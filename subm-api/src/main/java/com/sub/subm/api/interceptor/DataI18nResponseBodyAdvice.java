package com.sub.subm.api.interceptor;

import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import com.sub.common.annotation.DataI18n;
import com.sub.common.core.common.CommonResult;
import com.sub.subm.api.service.DataI18nService;
import com.sub.subm.dal.entity.generate.product.CurrencyPo;
import com.sub.subm.dal.entity.generate.product.ProductCategoryPo;
import com.sub.subm.dal.entity.generate.product.RegionPo;

/**
 * 数据国际化响应体拦截器
 * 在Controller返回响应前，自动对标记了@DataI18n注解的方法返回的数据进行国际化处理
 */
@ControllerAdvice
public class DataI18nResponseBodyAdvice implements ResponseBodyAdvice<Object> {

    private static final Logger log = LoggerFactory.getLogger(DataI18nResponseBodyAdvice.class);

    @Autowired
    private DataI18nService dataI18nService;

    // 使用ThreadLocal跟踪当前请求中已处理的对象，避免循环引用
    private static final ThreadLocal<Set<Object>> PROCESSED_OBJECTS = ThreadLocal.withInitial(ConcurrentHashMap::newKeySet);

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 检查方法或类是否标记了@DataI18n注解
        return returnType.hasMethodAnnotation(DataI18n.class) || 
               returnType.getDeclaringClass().isAnnotationPresent(DataI18n.class);
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                Class<? extends HttpMessageConverter<?>> selectedConverter,
                                ServerHttpRequest request, ServerHttpResponse response) {
        
        try {
            // 获取@DataI18n注解
            DataI18n dataI18nAnnotation = returnType.getMethodAnnotation(DataI18n.class);
            if (dataI18nAnnotation == null) {
                dataI18nAnnotation = returnType.getDeclaringClass().getAnnotation(DataI18n.class);
            }
            
            // 如果注解存在且启用了国际化
            if (dataI18nAnnotation != null && dataI18nAnnotation.value()) {
                return processDataI18n(body);
            }
        } catch (Exception e) {
            log.error("数据国际化处理失败: {}", e.getMessage(), e);
        }
        
        return body;
    }
    
    /**
     * 处理数据国际化
     * @param data 原始数据
     * @return 国际化后的数据
     */
    private Object processDataI18n(Object data) {
        if (data == null) {
            return null;
        }

        try {
            // 清空ThreadLocal，开始新的处理周期
            PROCESSED_OBJECTS.get().clear();

            // 处理CommonResult包装的数据
            if (data instanceof CommonResult) {
                CommonResult<?> result = (CommonResult<?>) data;
                Object processedData = processObject(result.getData());
                return CommonResult.ok(processedData);
            }

            // 直接处理数据
            return processObject(data);
        } finally {
            // 清理ThreadLocal，避免内存泄漏
            PROCESSED_OBJECTS.remove();
        }
    }
    
    /**
     * 处理对象数据
     * @param obj 对象
     * @return 处理后的对象
     */
    @SuppressWarnings("unchecked")
    private Object processObject(Object obj) {
        if (obj == null) {
            return null;
        }

        // 检查是否已经处理过此对象，避免循环引用
        Set<Object> processedObjects = PROCESSED_OBJECTS.get();
        if (processedObjects.contains(obj)) {
            return obj; // 直接返回原对象，避免循环处理
        }

        // 标记为已处理
        processedObjects.add(obj);

        try {
            // 处理Collection类型（List、Set等）
            if (obj instanceof Collection) {
                Collection<Object> collection = (Collection<Object>) obj;
                return collection.stream()
                        .map(this::processObject)
                        .collect(java.util.stream.Collectors.toList());
            }

            // 处理Map类型
            if (obj instanceof Map) {
                Map<String, Object> map = (Map<String, Object>) obj;
                map.forEach((key, value) -> map.put(key, processObject(value)));
                return map;
            }

            // 处理具体的实体类型
            return processEntity(obj);
        } finally {
            // 处理完成后从集合中移除，允许在其他地方重新处理
            processedObjects.remove(obj);
        }
    }
    
    /**
     * 处理实体对象
     * @param entity 实体对象
     * @return 处理后的实体对象
     */
    private Object processEntity(Object entity) {
        if (entity == null) {
            return null;
        }
        
        // 处理货币数据
        if (entity instanceof CurrencyPo) {
            return dataI18nService.internationalizeCurrency((CurrencyPo) entity);
        }
        
        // 处理地区数据
        if (entity instanceof RegionPo) {
            return dataI18nService.internationalizeRegion((RegionPo) entity);
        }
        
        // 处理产品分类数据
        if (entity instanceof ProductCategoryPo) {
            return dataI18nService.internationalizeProductCategory((ProductCategoryPo) entity);
        }
        
        // 处理包含嵌套对象的复杂实体
        return processComplexEntity(entity);
    }
    
    /**
     * 处理复杂实体对象（包含嵌套的需要国际化的对象）
     * @param entity 实体对象
     * @return 处理后的实体对象
     */
    private Object processComplexEntity(Object entity) {
        // 对于复杂实体，我们只处理已知的需要国际化的类型
        // 避免深度递归处理所有字段，防止循环引用和性能问题

        // 如果是基本类型或常见的不需要处理的类型，直接返回
        if (entity == null ||
            entity instanceof String ||
            entity instanceof Number ||
            entity instanceof Boolean ||
            entity instanceof java.util.Date ||
            entity instanceof java.time.LocalDateTime ||
            entity instanceof java.time.LocalDate ||
            entity.getClass().isPrimitive() ||
            entity.getClass().getPackage() != null &&
            (entity.getClass().getPackage().getName().startsWith("java.") ||
             entity.getClass().getPackage().getName().startsWith("javax."))) {
            return entity;
        }

        // 对于其他复杂对象，暂时不进行深度处理，避免循环引用
        // 如果需要处理特定的复杂对象，可以在processEntity方法中添加具体的处理逻辑
        return entity;
    }
}

package com.sub.subm.api.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.sub.common.utils.StringUtils;
import com.sub.subm.dal.dao.product.CurrencyDao;
import com.sub.subm.dal.entity.generate.product.CurrencyPo;

/**
 * 货币转换工具类
 * 基于USD作为中间货币实现任意货币间转换
 */
@Component
public class CurrencyConversionUtil {

    private static final Logger log = LoggerFactory.getLogger(CurrencyConversionUtil.class);
    
    @Autowired
    private CurrencyDao currencyDao;
    
    /**
     * 将金额从源货币转换为目标货币
     * 
     * @param amount 原始金额
     * @param fromCurrency 源货币代码（如：CNY）
     * @param toCurrency 目标货币代码（如：USD）
     * @return 转换后的金额
     */
    public BigDecimal convertCurrency(BigDecimal amount, String fromCurrency, String toCurrency) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        
        if (StringUtils.isEmpty(fromCurrency) || StringUtils.isEmpty(toCurrency)) {
            log.warn("货币代码不能为空: fromCurrency={}, toCurrency={}", fromCurrency, toCurrency);
            return amount;
        }
        
        // 如果源货币和目标货币相同，直接返回原金额
        if (fromCurrency.equalsIgnoreCase(toCurrency)) {
            return amount;
        }
        
        try {
            // 获取汇率
            BigDecimal conversionRate = getConversionRate(fromCurrency, toCurrency);
            if (conversionRate == null) {
                log.warn("无法获取汇率: {} -> {}", fromCurrency, toCurrency);
                return amount;
            }
            
            // 执行转换，保留4位小数
            BigDecimal convertedAmount = amount.multiply(conversionRate).setScale(4, RoundingMode.HALF_UP);
            log.debug("货币转换: {} {} -> {} {} (汇率: {})", 
                amount, fromCurrency, convertedAmount, toCurrency, conversionRate);
            
            return convertedAmount;
        } catch (Exception e) {
            log.error("货币转换失败: {} {} -> {}, 错误: {}", amount, fromCurrency, toCurrency, e.getMessage());
            return amount;
        }
    }
    
    /**
     * 获取从源货币到目标货币的汇率
     * 使用USD作为中间货币：源货币 -> USD -> 目标货币
     * 
     * @param fromCurrency 源货币代码
     * @param toCurrency 目标货币代码
     * @return 汇率，如果无法获取则返回null
     */
    public BigDecimal getConversionRate(String fromCurrency, String toCurrency) {
        if (StringUtils.isEmpty(fromCurrency) || StringUtils.isEmpty(toCurrency)) {
            return null;
        }
        
        // 如果源货币和目标货币相同，汇率为1
        if (fromCurrency.equalsIgnoreCase(toCurrency)) {
            return BigDecimal.ONE;
        }
        
        try {
            // 获取所有货币汇率（对USD）
            Map<String, BigDecimal> currencyRates = getAllCurrencyRates();
            
            // 如果其中一个是USD，直接计算
            if ("USD".equalsIgnoreCase(fromCurrency)) {
                BigDecimal toRate = currencyRates.get(toCurrency.toUpperCase());
                return toRate != null ? BigDecimal.ONE.divide(toRate, 6, RoundingMode.HALF_UP) : null;
            }
            
            if ("USD".equalsIgnoreCase(toCurrency)) {
                return currencyRates.get(fromCurrency.toUpperCase());
            }
            
            // 两个都不是USD，通过USD中转
            BigDecimal fromRate = currencyRates.get(fromCurrency.toUpperCase());
            BigDecimal toRate = currencyRates.get(toCurrency.toUpperCase());
            
            if (fromRate == null || toRate == null) {
                log.warn("无法找到货币汇率: fromCurrency={}, toRate={}", fromCurrency, toCurrency);
                return null;
            }
            
            // 计算转换汇率：(1 / fromRate) * toRate = toRate / fromRate
            BigDecimal conversionRate = toRate.divide(fromRate, 6, RoundingMode.HALF_UP);
            log.debug("计算汇率: {} -> {} = {} (通过USD: {} -> {})", 
                fromCurrency, toCurrency, conversionRate, fromRate, toRate);
            
            return conversionRate;
        } catch (Exception e) {
            log.error("获取汇率失败: {} -> {}, 错误: {}", fromCurrency, toCurrency, e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取所有货币对USD的汇率映射
     * 
     * @return 货币代码 -> 汇率的映射
     */
    private Map<String, BigDecimal> getAllCurrencyRates() {
        List<CurrencyPo> currencies = currencyDao.filterCurrencyList();
        return currencies.stream()
            .filter(currency -> StringUtils.isNotEmpty(currency.getSimple()) && currency.getExchangeRate() != null)
            .collect(Collectors.toMap(
                currency -> currency.getSimple().toUpperCase(),
                CurrencyPo::getExchangeRate,
                (existing, replacement) -> existing // 如果有重复key，保留现有值
            ));
    }
    
    /**
     * 获取指定货币对USD的汇率
     * 
     * @param currencyCode 货币代码
     * @return 汇率，如果不存在则返回null
     */
    public BigDecimal getCurrencyToUsdRate(String currencyCode) {
        if (StringUtils.isEmpty(currencyCode)) {
            return null;
        }
        
        if ("USD".equalsIgnoreCase(currencyCode)) {
            return BigDecimal.ONE;
        }
        
        CurrencyPo currency = currencyDao.getCurrencyName(currencyCode.trim());
        return currency != null ? currency.getExchangeRate() : null;
    }
    
    /**
     * 检查货币代码是否有效
     * 
     * @param currencyCode 货币代码
     * @return 是否有效
     */
    public boolean isValidCurrency(String currencyCode) {
        if (StringUtils.isEmpty(currencyCode)) {
            return false;
        }
        
        if ("USD".equalsIgnoreCase(currencyCode)) {
            return true;
        }
        
        CurrencyPo currency = currencyDao.getCurrencyName(currencyCode.trim());
        return currency != null && currency.getExchangeRate() != null;
    }
}

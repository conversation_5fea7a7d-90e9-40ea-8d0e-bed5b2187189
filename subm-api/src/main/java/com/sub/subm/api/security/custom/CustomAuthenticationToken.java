package com.sub.subm.api.security.custom;

import java.util.Collection;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.util.Assert;

/**
 * 自定义 封装 用户认证信息类
 * 支持多渠道 认证
 */
public class CustomAuthenticationToken extends AbstractAuthenticationToken {
    private static final long serialVersionUID = 610L;
    private Object principal;
    private Object credentials;

    // 认证渠道: 如: 微信, 手机号码, 邮箱
    private Integer channel;
    // 认证类别:
    private Integer type;
    private String account;
    private String pwd;

    public CustomAuthenticationToken(String account, String pwd, Integer channel, Integer type) {
        super(null);
        this.account = account;
        this.pwd = pwd;
        this.channel = channel;
        this.type = type;
    }

    public CustomAuthenticationToken(Object principal, Object credentials, Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.principal = principal;
        this.credentials = credentials;
        super.setAuthenticated(true);
    }


    public Integer getChannel() {
        return channel;
    }

    public Integer getType() {
        return type;
    }

    public String getAccount() {
        return account;
    }

    public String getPwd() {
        return pwd;
    }

    public Object getCredentials() {
        return this.credentials;
    }

    public Object getPrincipal() {
        return this.principal;
    }

    public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {
        Assert.isTrue(!isAuthenticated, "Cannot set this token to trusted - use constructor which takes a GrantedAuthority list instead");
        super.setAuthenticated(false);
    }

    public void eraseCredentials() {
        super.eraseCredentials();
        this.credentials = null;
    }
}

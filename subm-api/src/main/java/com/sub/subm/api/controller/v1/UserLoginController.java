package com.sub.subm.api.controller.v1;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.sub.common.constant.CacheConstants;
import com.sub.common.core.common.CommonResult;
import com.sub.common.exception.BaseException;
import com.sub.common.utils.StringUtils;
import com.sub.framework.config.ServerConfig;
import com.sub.subm.api.entity.dto.UserLoginDTOs;
import com.sub.subm.api.entity.vo.LoginUser;
import com.sub.subm.api.security.SecurityUtils;
import com.sub.subm.api.security.TokenUtil;
import com.sub.subm.api.service.account.UserLoginService;
import com.sub.subm.api.service.account.VerifyCodeService;
import com.sub.subm.dal.entity.generate.user.UserPo;
import com.sub.subm.dal.service.SettingResultServiceImpl;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping(value = "/api/v1")
@Tag(name = "登录, 注册 模块")
public class UserLoginController extends CommonResult {


    @Autowired
    private VerifyCodeService verifyCodeService;
    @Autowired
    private UserLoginService userLoginService;
    @Autowired
    private TokenUtil tokenUtil;
    @Autowired
    private SettingResultServiceImpl settingResultService;
    @Autowired
    private ServerConfig serverConfig;


    @Operation(summary = "发送验证码")
    @RequestMapping(value = "sendVerifyCode", method = RequestMethod.POST)
    public CommonResult sendVerifyCode(@RequestBody @Validated UserLoginDTOs.SendVerifyDTO data) throws BaseException {
        // 验证账号渠道是否合法
        verifyCodeService.verifyChannelOpen(data.getChannel(), data.getAccount());
        // 验证账号是否 符合发送验证码 条件
        UserPo userPo = userLoginService.getUserDetail(data.getAccount(), data.getChannel());
        verifyCodeService.verifyTypeAccount(userPo, data.getType());
        // 发送 验证码
        verifyCodeService.generateVerifyCode(data);
        return ok();
    }

    @Operation(summary = "验证码是否正确")
    @RequestMapping(value = "verifyCode", method = {RequestMethod.POST})
    public CommonResult verifyCode(@RequestBody @Validated UserLoginDTOs.VerifyPwdDTO data)
            throws BaseException {
        // 验证账号渠道
        verifyCodeService.verifyChannelOpen(data.getChannel(), data.getAccount());
        // 验证key 是否正确
        String redisKey = CacheConstants.VERIFY  + data.getChannel() + ":" +
                data.getType() + ":"+ data.getAccount();
        verifyCodeService.verifyCode(redisKey, data.getVerifyCode());
        return ok();
    }


    @Operation(summary = "登录成功, 需要验证2fa 才能进入系统")
    @RequestMapping(value = "2faVerify", method = RequestMethod.POST)
    public CommonResult verify2faCode(@RequestBody @Validated UserLoginDTOs.TwoLoginDTO data) throws BaseException {
        return ok();
    }

    @Operation(summary = "用户登录")
    @RequestMapping(value = "login", method = RequestMethod.POST)
    public CommonResult login(@RequestBody @Validated UserLoginDTOs.UserLoginDTO data) throws BaseException {
        // 验证账号渠道 错误抛出异常
        verifyCodeService.verifyChannelOpen(data.getChannel(), data.getAccount());
        // 用户登录信息
        LoginUser loginUser = userLoginService.userLogin(data.getAccount(), data.getChannel(), data.getType(), data.getPwd());
//        if (loginUser.getTwoEnable() == 1) {
//            // 需要2fa 验证, 则不返回token
//            return ok(loginUser.getUserId());
//        }
        String token = tokenUtil.generateUserToken(loginUser, data.getOperation());
//        AsyncManager.me().execute(AsyncFactory.loginSuccessHandler(loginUser.getUserId(), data.getAccount(), data.getChannel()));
        return okToken(token);
    }

    @Operation(summary = "注册")
    @RequestMapping(value = "register", method = RequestMethod.POST)
    public CommonResult register(@RequestBody @Validated UserLoginDTOs.RegisterTDO data) throws BaseException {
        // 验证 系统是否 开启注册功能
        verifyCodeService.enableRegister();
        data.setName("Sub" + StringUtils.verifySalt(2));
        // 验证账号是否 符合 注册功能
        UserPo userPo = userLoginService.getUserDetail(data.getAccount(), data.getChannel());
        verifyCodeService.verifyTypeAccount(userPo, VerifyCodeService.MailSubject.REGISTER.ordinal());
        // 验证key 是否正确
        String redisKey = CacheConstants.VERIFY  + data.getChannel() + ":" +
                VerifyCodeService.MailSubject.REGISTER.ordinal() + ":"+ data.getAccount();
        verifyCodeService.verifyCode(redisKey, data.getVerifyCode());
        // 注册
        userLoginService.builderSysUserPo(data);
        // 删除 key
        verifyCodeService.deleteVerifyCode(redisKey);
        return ok();
    }

    @Operation(summary = "忘记密码并进行修改")
    @RequestMapping(value = "forgetPwd", method = RequestMethod.POST)
    public CommonResult forgetPwd(@RequestBody UserLoginDTOs.ForPwdDTO data) throws BaseException {
        // 验证账号是否 符合 注册功能
        UserPo userPo = userLoginService.getUserDetail(data.getAccount(), data.getChannel());
        verifyCodeService.verifyTypeAccount(userPo, VerifyCodeService.MailSubject.FORGET.ordinal());
        // 验证key 是否正确
        String redisKey = CacheConstants.VERIFY + data.getChannel() + ":" +
                VerifyCodeService.MailSubject.FORGET.ordinal() + ":"+ data.getAccount();
        verifyCodeService.verifyCode(redisKey, data.getVerifyCode());
        // 修改用户登录 密码
        userLoginService.updateUserLoginPwd(userPo.getId(), data.getPwd());
        // 删除 key
        verifyCodeService.deleteVerifyCode(redisKey);
        return ok();
    }


    @Operation(summary = "三方登录: 1: 系统中存在则直接登录 2: 不存在则直接注册")
    @RequestMapping(value = "authLoginRegister", method = {RequestMethod.POST})
    public CommonResult authLoginRegister(@RequestBody UserLoginDTOs.AuthRegisterDTO data) throws BaseException {
        // 验证账号渠道
        verifyCodeService.verifyChannelOpen(data.getChannel(), data.getUuid());
        userLoginService.authRegister(data);
        LoginUser loginUser = userLoginService.userLogin(data.getUuid(), data.getChannel(), 3, null);

        String token = tokenUtil.generateUserToken(loginUser, true);
//        AsyncManager.me().execute(AsyncFactory.loginSuccessHandler(loginUser.getUserId(), data.getAccount(), data.getChannel()));
        return okToken(token);
    }



    @Operation(summary = "获取用户信息")
    @RequestMapping(value = "getUserInfo", method = RequestMethod.GET)
    public CommonResult getInfo() throws BaseException {
        String id = SecurityUtils.getLoginUser().getUserId();
        UserPo userPo = userLoginService.getUserBasicInfo(id);
        if (StringUtils.isNotEmpty(userPo.getAvatar()) && !userPo.getAvatar().startsWith("http")) {
            userPo.setAvatar(serverConfig.getUrl() + userPo.getAvatar());
        }
        return ok(userPo);
    }

}

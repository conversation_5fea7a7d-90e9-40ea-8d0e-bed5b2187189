package com.sub.subm.api.service.account;

import java.util.Arrays;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.sub.common.constant.CacheConstants;
import com.sub.common.core.redis.RedisCache;
import com.sub.common.exception.BaseException;
import com.sub.common.mail.MailUtil;
import com.sub.common.mail.SendMailBo;
import com.sub.common.utils.StringUtils;
import com.sub.common.utils.TimeUtils;
import com.sub.subm.api.entity.dto.UserLoginDTOs;
import com.sub.subm.dal.constant.SettingResultConstant;
import com.sub.subm.dal.entity.generate.user.UserPo;
import com.sub.subm.dal.service.SettingResultServiceImpl;

@Service
public class VerifyCodeService {
    private Logger log = LoggerFactory.getLogger(VerifyCodeService.class);

    @Autowired
    private SettingResultServiceImpl settingResultServiceImpl;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private MailUtil mailUtil;

    /**
     * 验证码
     * @param redisKey
     * @param verifyCode
     */
    public void verifyCode(String redisKey, String verifyCode) throws BaseException {
        String getValue = redisCache.getCacheObject(redisKey);
        if (StringUtils.isEmpty(getValue)) {
            log.warn("验证码 为空 redisKey为" + redisKey);
            throw new BaseException(-100, "user.jcaptcha.expire");
        }
        if (!getValue.equals(verifyCode)) {
            // 验证码错误
            throw new BaseException(-100, "user.jcaptcha.error");
        }
    }

    /**
     * 验证一个用户 2fa 验证方式
     * @param userId
     */
//    public void twoVerify(String userId, String verifyCode) throws BaseException {
//        SysUserPo userPo = sysUserDao.getByIdUser(userId);
//        if (userPo == null || userPo.getEnable() == 0) {
//            log.warn("用户{} 不存在", userId);
//            throw new BaseException(-100, "user.not.exists");
//        }
//        if (userPo.getEnable() != 1) {
//            log.warn("用户{} 状态不正常, {}", userId, userPo.getEnable());
//            throw new BaseException(-100, "user.blocked");
//        }
//        String secretCode = null;
//        try {
//            secretCode = TimeBasedOneTimePasswordUtil.generateCurrentNumberString(userPo.getTwoSecret());
//        } catch (GeneralSecurityException e) {
//            log.warn("2fa 验证生成代码 异常, 请检查服务器时区");
//            throw new BaseException(-300, "user.jcaptcha.expire");
//        }
//        if (!secretCode.equals(verifyCode)) {
//            log.warn("{}的安全码错误, 用户输入的安全码是{}", userId, verifyCode);
//            throw new BaseException(-100, "user.jcaptcha.error");
//        }
//    }


    /**
     * 验证当前系统 是否开启 注册功能
     * @throws BaseException
     */
    public void enableRegister() throws BaseException {
        String registerStr = settingResultServiceImpl.getAppointNameStr(SettingResultConstant.REGISTER_ENABLE);
        if (registerStr.equals("0")) {
            // 当前系统还未开启注册功能
            throw new BaseException(0, "enable.register");
        }
    }

    /**
     *  验证渠道 和 账号 是否 合法
     * @param channel
     * @throws BaseException
     */
    public void verifyChannelOpen(Integer channel, String account) throws BaseException {
        String getChannelStr = settingResultServiceImpl.getAppointNameStr(SettingResultConstant.LOGIN_METHOD);
        List<String> channelList = Arrays.asList(getChannelStr.split(","));
        if (!channelList.contains(String.valueOf(channel))) {
            log.warn("渠道类型不符合后台规则 {}", channel);
            throw new BaseException(-400, "param.exception");
        }
        // 邮箱验证
        if (channel == 0) {
            List<String> supportList = Arrays.asList(settingResultServiceImpl.getAppointNameStr(SettingResultConstant.SUPPORT_EMAIL_LIST).split(","));
            if (!StringUtils.verifyEmailLegal(account, supportList)) {
                log.warn("邮箱后缀不符合后台规则{}", account);
                throw new BaseException(-401, "not.support.email");
            }
        }

    }


    /**
     * 生成验证码, 并进行发送
     * @param sendVerify
     */
    public String generateVerifyCode(UserLoginDTOs.SendVerifyDTO sendVerify) throws BaseException {
        // 生成6位存数字验证码
        String verifyCode = StringUtils.verifyCode();
        String redisKey = CacheConstants.VERIFY + sendVerify.getChannel() + ":" + sendVerify.getType() +  ":" + sendVerify.getAccount();
        if (sendVerify.getChannel() == 0) {
            // 发送邮件内容
            builderVerifyMailContent(sendVerify, redisKey, verifyCode);
        }
        return verifyCode;
    }


    @Value("${project.name}")
    private String project;
    protected void builderVerifyMailContent(UserLoginDTOs.SendVerifyDTO sendVerify,
                                            String redisKey,
                                            String verifyCode) throws BaseException {
        String sendContent = settingResultServiceImpl.getAppointNameStr(SettingResultConstant.LOGIN_REGISTER_MAIL);
        String subject = MailSubject.getMailSubjectValue(sendVerify.getType());
        SendMailBo sendMailBo = SendMailBo.builder()
                .account(sendVerify.getAccount())
                .subject(subject)
                .redisKey(redisKey)
                .redisValue(verifyCode)
                .text(StringUtils.replace(sendContent, "{0}", verifyCode))
                .project(project)
                .isVerifyEmail(true)
                .createTime(TimeUtils.getCurrentTime())
                .build();
        mailUtil.sendEmail(sendMailBo);
        log.info("邮件已发送 账号{} 验证类型{}", sendVerify.getAccount(), sendVerify.getType());
    }


    /**
     * 验证账号是否符合 查询类型
     * @param userPo
     * @param type
     */
    public void verifyTypeAccount(UserPo userPo, Integer type) throws BaseException {
        MailSubject mailSubject = MailSubject.getMailSubject(type);
        switch (mailSubject) {
            case REGISTER:
                if (userPo != null) {
                    log.warn("该账号已在系统中注册");
                    throw new BaseException(-100, "account.exist");
                }
                break;
            case FORGET:
            case UPDATE:
            case BINDING:
            case LOGIN_VERIFY:
            case SYS_VERIFY:
                if (userPo == null) {
                    log.warn("该账号还未在系统中注册");
                    throw new BaseException(-100, "account.not.exist");
                }
                break;
            default:
                throw new BaseException(-400, "param.exception");
        }
    }

    /**
     * 删除缓存键
     */
    public void deleteVerifyCode(String redisKey) {
        try {
            redisCache.deleteObject(redisKey);
        } catch (Exception e) {
            log.error("Redis 缓存键删除失败: "+ redisKey);
        }
    }


    /**
     * 邮件主题 与 账号类型验证
     */
    public enum MailSubject {
        REGISTER("欢迎注册"),
        FORGET("找回密码"),
        UPDATE("修改密码"),
        BINDING("账号绑定"),
        LOGIN_VERIFY("登录验证"),
        SYS_VERIFY("账号验证")
        ;
        private String value;


        private static Logger log = LoggerFactory.getLogger(MailSubject.class);
        MailSubject(String value) {
            this.value = value;
        }
        public String getValue() {
            return value;
        }
        public static MailSubject getMailSubject(Integer index) throws BaseException {
            Integer length = MailSubject.values().length;
            if (index < 0 || index >= length) {
                log.error("类型下标超出范围, 总{}, 使用{}", length, index);
                throw new BaseException(-400, "param.exception");
            }
            return MailSubject.values()[index];
        }
        public static String getMailSubjectValue(Integer index) throws BaseException {
            return getMailSubject(index).getValue();
        }
    }
}

package com.sub.subm.api.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sub.common.utils.StringUtils;
import com.sub.common.utils.TimeUtils;
import com.sub.common.utils.auth.AuthUtils;
import com.sub.subm.api.security.SecurityUtils;
import com.sub.subm.dal.dao.user.UserDao;
import com.sub.subm.dal.dao.user.UserDeviceDao;
import com.sub.subm.dal.entity.generate.user.UserPo;

import jakarta.servlet.http.HttpServletRequest;

@Service
public class DataRecordServiceImpl {
    private Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private UserDeviceDao userDeviceDao;
    @Autowired
    private UserDao userDao;

    public void recordRequestLog(HttpServletRequest request, String ipAddress) {
        log.info("记录请求日志");
        String userId = SecurityUtils.getUserIdNotE();
        Long currentTime = TimeUtils.getCurrentTime();
        // 设备 id
        String deviceId = request.getHeader("S-Device-Id");
        // 操作系统类型
        String osType = request.getHeader("S-Os-Type");
        // 操作系统版本
        String osVersion = request.getHeader("S-Os-Version");
        // app 版本
        String appVersion = request.getHeader("S-App-Version");
        // 设备型号
        String deviceModel = request.getHeader("S-Device-Model");
        String groupHash = "";
        if (StringUtils.isEmpty(userId)) {
            groupHash = AuthUtils.md5Hex(groupHash);
        } else {
            groupHash = AuthUtils.md5Hex(userId + deviceId);
        }
//        UserDevicePo userDevicePo = userDeviceDao.getByUserDevice(deviceId);
//        if (userDevicePo == null && StringUtils.isEmpty(userId)) {
//            userDevicePo = new UserDevicePo();
//            userDevicePo.setUserId(userId);
//            userDevicePo.setDeviceId(deviceId);
//            userDevicePo.setCreateTime(currentTime);
//            userDevicePo.setUpdateTime(currentTime);
//            userDevicePo.setCreateIp(ipAddress);
//            userDevicePo.setIpAddress(ipAddress);
//
//            userDevicePo.setDeviceModel(deviceModel);
//            userDevicePo.setOsType(osType);
//            userDevicePo.setOsVersion(osVersion);
//            userDevicePo.setAppVersion(appVersion);
//            userDevicePo.setGroupHash(groupHash);
//            userDevicePo.setIsGuest(1);
//            userDeviceDao.insertUserDevice(userDevicePo);
//        }
//
//        if (userDevicePo != null && StringUtils.isNotEmpty(userId)) {
//            userDevicePo.setUpdateTime(currentTime);
//            userDevicePo.setGroupHash(groupHash);
//            userDevicePo.setIpAddress(ipAddress);
//            userDeviceDao.updateUserDevice(userDevicePo);
//        }
//
//        if (userDevicePo == null && StringUtils.isNotEmpty(userId)) {
//            UserDevicePo devicePo = userDeviceDao.getByHashUserDevice(groupHash);
//            if (devicePo != null) {
//                devicePo.setUpdateTime(currentTime);
//                devicePo.setIpAddress(ipAddress);
//                userDeviceDao.updateUserDevice(devicePo);
//            }
//        }
    }



    public void  recordLoginLog(HttpServletRequest request, String userId,
                                String ipAddress, Long currentTime,
                                String account, Integer channel) {
        log.info("记录登录日志");
        // 设备 id
        String deviceId = request.getHeader("S-Device-Id");
        String groupHash = AuthUtils.md5Hex(userId + deviceId);
        UserPo userPo = new UserPo();
        userPo.setId(userId);
        userPo.setLastOnlineTime(currentTime);
        userPo.setIpAddress(ipAddress);
//        UserDevicePo devicePo = userDeviceDao.getByHashUserDevice(groupHash);
//        if (devicePo == null) {
//            devicePo = new UserDevicePo();
//            devicePo.setOsType(request.getHeader("S-Os-Type"));
//            devicePo.setOsVersion(request.getHeader("S-Os-Version"));
//            devicePo.setAppVersion(request.getHeader("S-App-Version"));
//            devicePo.setDeviceModel(request.getHeader("S-Device-Model"));
//            devicePo.setGroupHash(groupHash);
//            devicePo.setIsGuest(0);
//            devicePo.setUserId(userId);
//            devicePo.setDeviceId(deviceId);
//            devicePo.setCreateIp(ipAddress);
//            devicePo.setIpAddress(ipAddress);
//            devicePo.setUpdateTime(currentTime);
//            devicePo.setCreateTime(currentTime);
//            userDeviceDao.insertUserDevice(devicePo);
//        }
//        userPo.setDeviceId(deviceId);
//        userDao.updateUser(userPo);
    }

}

package com.sub.subm.api.service;


import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.sub.common.core.redis.RedisCache;
import com.sub.subm.dal.dao.product.CurrencyDao;
import com.sub.subm.dal.entity.generate.product.CurrencyPo;

@Component
public class ExchangRateService {

    private Logger log = LoggerFactory.getLogger(this.getClass());

    private static final String CHOOSE_CURRENCY = "CNY";
    private static final String REQUEST_URL = "https://jisuhuilv.market.alicloudapi.com/exchange/single?currency=";
    private static final String APPCODE = "APPCODE cb89f021efdf47b79d351f3e5500091d";

    @Autowired
    private CurrencyDao currencyDao;
    @Autowired
    private RedisCache redisCache;

    public List<CurrencyPo> getDbCurrencyList() {
        List<CurrencyPo> list = currencyDao.filterCurrencyList();
        return list;
    }

    public String getCurrencySymbol(String currency) {
        CurrencyPo currencyPo = currencyDao.getCurrencyName(currency);
        return currencyPo.getSymbol();
    }
}

package com.sub.subm.api.config;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import com.alibaba.fastjson2.JSONObject;
import com.sub.common.mail.ConfigMailPo;
import com.sub.common.mail.MailUtil;
import com.sub.common.utils.StringUtils;
import com.sub.subm.dal.constant.SettingResultConstant;
import com.sub.subm.dal.entity.generate.config.SettingRulePo;
import com.sub.subm.dal.service.SettingResultServiceImpl;

import jakarta.annotation.PostConstruct;

@Configuration
public class ProjectConfig {
    private Logger log = LoggerFactory.getLogger(ProjectConfig.class);

    @Autowired
    private SettingResultServiceImpl settingResultServiceImpl;
    @Autowired
    private MailUtil mailUtil;


    @PostConstruct
    public void initProjectConfig() {
        settingResultServiceImpl.initSettingResult();
        mailInitConfig();
    }

    /**
     * 发件邮箱配置
     */
    public void mailInitConfig() {
        SettingRulePo emailSettingPo = settingResultServiceImpl.getAppointName(SettingResultConstant.SEND_EMAIL_CONFIG);
        if (emailSettingPo != null && !StringUtils.isEmpty(emailSettingPo.getValue())) {
            try {
                ConfigMailPo configMailPo = JSONObject.parseObject(emailSettingPo.getValue(), ConfigMailPo.class);
                configMailPo = ConfigMailPo.decodeEmailConfig(configMailPo);
                log.info(JSONObject.toJSONString(configMailPo));
                mailUtil.initConfigEmail(configMailPo);
            } catch (Exception e) {
                log.warn("邮箱配置失败!!!! 具体错误如下" + e);
            }
        }
    }
}

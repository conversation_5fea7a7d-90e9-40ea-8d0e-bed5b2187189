package com.sub.subm.api.entity.vo;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class filterRemindVO {
    @Schema(description = "唯一编号id")
    private String id;
    @Schema(description = "用户id")
    private String userId;
    @Schema(description = "图片")
    private String img;
    @Schema(description = "产品id")
    private String productId;
    @Schema(description = "产品名称")
    private String productName;
    @Schema(description = "套餐名称")
    private String planName;
    @Schema(description = "地区id")
    
    private String regionId;
    @Schema(description = "免费数值")
    private Long freeNum;
    @Schema(description = "免费类型: 天 周 日 月 季 年")
    private Integer freeType;
    @Schema(description = "提醒周期 0永久 1年 3月 4周 5日")
    private Integer remindCycle;
    @Schema(description = "创建时间戳")
    private Long createTime;
    @Schema(description = "开始时间")
    private Long startTime;
    @Schema(description = "结束日期")
    private Long endTime;

    @Schema(description = "价格")
    private BigDecimal amount;
    @Schema(description = "汇率")
    private BigDecimal rate;

    @Schema(description = "货币")
    private String currency;
    @Schema(description = "描述")
    private String desc;
    @Schema(description = "状态")
    private Integer status;
    @Schema(description = "提醒类型")
    private Integer remindType;

    @Schema(description = "是否为自定义")
    private Integer isCustom;

    @Schema(description = "修改时间")
    private Long updateTime;

    @Schema(description = "种类")
    private String category;

    @Schema(description = "下次提醒(续费)时间")
    private Long remindNextTime;
}

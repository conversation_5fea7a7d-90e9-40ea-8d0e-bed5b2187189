package com.sub.subm.api.service.assistant.context;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.sub.common.exception.BaseException;
import com.sub.subm.api.service.assistant.model.Session;

import lombok.extern.slf4j.Slf4j;

/**
 * 会话上下文
 */
@Slf4j
@Component
public class SessionContext {

    private final Map<String, Session> sessions = new ConcurrentHashMap<>();

    /**
     * 获取会话，如果不存在则抛出异常
     */
    public Session getSession(String sessionId) throws BaseException {
        Session session = sessions.get(sessionId);
        if (session == null) {
            throw new BaseException("会话不存在");
        } else {
            session.updateLastActiveTime();
        }
        return session;
    }

    /**
     * 移除指定会话
     */
    public void removeSession(String sessionId) {
        Session removedSession = sessions.remove(sessionId);
        if (removedSession != null) {
            log.info("移除会话, sessionId: {}, userId: {}", sessionId, removedSession.getUserId());
        }
    }

    /**
     * 清空所有会话
     */
    public void clearSessions() {
        int count = sessions.size();
        sessions.clear();
        log.info("清空所有会话, 共清理 {} 个会话", count);
    }

    /**
     * 创建用户会话
     */
    public String createUserSession(String userId, String userName, String currency) {
        Session session = new Session(userId, userName);
        session.setCurrency(currency);
        sessions.put(session.getSessionId(), session);
        log.info("创建用户会话, sessionId: {}, userId: {}, userName: {}",
                session.getSessionId(), userId, userName);
        return session.getSessionId();
    }

    /**
     * 检查会话是否存在
     */
    public boolean sessionExists(String sessionId) {
        return sessions.containsKey(sessionId);
    }

    /**
     * 获取活跃会话数量
     */
    public int getActiveSessionCount() {
        return (int) sessions.values().stream()
                .filter(session -> "ACTIVE".equals(session.getStatus()) && !session.isExpired())
                .count();
    }

    /**
     * 获取总会话数量
     */
    public int getTotalSessionCount() {
        return sessions.size();
    }

    /**
     * 清理过期会话
     */
    public void clearExpiredSessions() {
        LocalDateTime now = LocalDateTime.now();

        Map<String, Session> expiredSessions = sessions.entrySet().stream()
                .filter(entry -> entry.getValue().isExpired())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        expiredSessions.forEach((sessionId, session) -> {
            sessions.remove(sessionId);
            log.info("清理过期会话, sessionId: {}, userId: {}, lastActiveTime: {}",
                    sessionId, session.getUserId(), session.getLastActiveTime());
        });

        if (!expiredSessions.isEmpty()) {
            log.info("清理过期会话完成, 共清理 {} 个会话", expiredSessions.size());
        }
    }

    // /**
    //  * 定时清理过期会话 - 每10分钟执行一次
    //  */
    // @Scheduled(fixedRate = 600000) // 10分钟
    // public void scheduledCleanup() {
    //     try {
    //         clearExpiredSessions();
    //     } catch (Exception e) {
    //         log.error("定时清理过期会话失败", e);
    //     }
    // }

    /**
     * 获取用户的所有会话
     */
    public Map<String, Session> getUserSessions(String userId) {
        return sessions.entrySet().stream()
                .filter(entry -> userId.equals(entry.getValue().getUserId()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    /**
     * 更新会话状态
     */
    public void updateSessionStatus(String sessionId, String status) {
        Session session = sessions.get(sessionId);
        if (session != null) {
            session.setStatus(status);
            session.updateLastActiveTime();
            log.info("更新会话状态, sessionId: {}, status: {}", sessionId, status);
        }
    }
}

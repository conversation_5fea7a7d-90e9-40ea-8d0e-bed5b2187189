package com.sub.subm.api.service.assistant.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 工具回调响应对象
 * 
 * 用于统一格式返回工具执行结果，包含工具调用相关的完整元数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ToolCallResponse {

    /**
     * 工具名称
     * 例如：database_query、file_operation、api_call 等
     */
    private String toolName;

    /**
     * 工具执行结果
     * 可以是任何类型的数据结构：字符串、对象、列表、布尔值等
     */
    private Object toolResult;

    /**
     * 工具返回结果的描述信息
     * 提供人类可读的执行结果摘要
     */
    private String toolResultDescription;

    /**
     * 工具执行状态
     * 例如：success、failed、partial_success 等
     */
    private String toolStatus;

    /**
     * 工具执行耗时（毫秒）
     */
    private Long executionTime;

    /**
     * 工具执行错误信息
     * 当执行失败时提供具体的错误详情
     */
    private String errorMessage;

    /**
     * 工具版本信息
     * 用于追踪不同版本的工具行为和兼容性
     */
    private String toolVersion;
}

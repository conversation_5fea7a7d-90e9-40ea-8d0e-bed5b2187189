package com.sub.subm.api.service;

import java.math.BigDecimal;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.sub.common.constant.CacheConstants;
import com.sub.common.core.redis.RedisCache;
import com.sub.common.exception.BaseException;
import com.sub.common.utils.StringUtils;
import com.sub.common.utils.TimeUtils;
import com.sub.subm.api.utils.CurrencyConversionUtil;
import com.sub.subm.dal.dao.remind.FgRemindDao;
import com.sub.subm.dal.dao.remind.FgRemindInfoDao;
import com.sub.subm.dal.dao.user.UserDao;
import com.sub.subm.dal.entity.generate.remind.FgRemindInfoPo;
import com.sub.subm.dal.entity.generate.remind.FgRemindPo;
import com.sub.subm.dal.entity.generate.user.UserPo;

@Service
public class UserServiceImpl {
    private Logger log = LoggerFactory.getLogger(UserServiceImpl.class);

    @Autowired
    private UserDao userDao;

    @Autowired
    private CurrencyConversionUtil currencyConversionUtil;

    @Autowired
    private FgRemindDao fgRemindDao;

    @Autowired
    private FgRemindInfoDao fgRemindInfoDao;

    @Autowired
    private RedisCache redisCache;

    public UserPo getByIdUserPo(String userId) throws BaseException {
        if (StringUtils.isEmpty(userId)) {
            throw new BaseException(-400, "err.server");
        }
        UserPo userPo = userDao.getByIdUser(userId);
        if (userPo == null) {
            throw new BaseException(-404, "data.no.exist");
        }
        return userPo;
    }

    public void updateUser(UserPo userPo) {
        userPo.setUpdateTime(TimeUtils.getCurrentTime());
        userPo.setLastOnlineTime(userPo.getUpdateTime());
        userDao.updateUser(userPo);
    }



    /**
     * 注销
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void unsubscribeAccount(String userId) {
        UserPo userPo = new UserPo();
        userPo.setId(userId);
        userPo.setEnable(0);
        userDao.updateUser(userPo);
    }

    /**
     * 用户更改货币选项
     * 更新用户偏好货币并重新计算所有提醒记录的汇率
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void updateCurrency(String userId, String currency) throws BaseException {
        if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(currency)) {
            throw new BaseException(-400, "用户ID和货币代码不能为空");
        }

        // 验证货币代码是否有效
        if (!currencyConversionUtil.isValidCurrency(currency)) {
            throw new BaseException(-400, "无效的货币代码: " + currency);
        }

        log.info("用户 {} 更改货币选项为: {}", userId, currency);

        try {
            // 获取用户当前货币设置
            UserPo currentUser = getByIdUserPo(userId);
            String oldCurrency = currentUser.getCurrency();

            // 如果货币没有变化，直接返回
            if (currency.equalsIgnoreCase(oldCurrency)) {
                log.info("用户 {} 的货币设置未发生变化: {}", userId, currency);
                return;
            }

            // 更新用户货币设置
            UserPo userPo = new UserPo();
            userPo.setId(userId);
            userPo.setCurrency(currency);
            userPo.setUpdateTime(TimeUtils.getCurrentTime());
            userDao.updateUser(userPo);

            // 更新用户所有提醒记录的汇率
            updateUserRemindRates(userId, oldCurrency, currency);

            log.info("成功更新用户 {} 的货币设置为: {}", userId, currency);

            // 清除用户缓存
            if (StringUtils.isEmpty(userId)) {
                return;
            }
    
            // 清理提醒列表缓存
            String listCacheKey = CacheConstants.REMIND_LIST_KEY + ":" + userId;
            try {
                boolean deleted = redisCache.deleteObject(listCacheKey);
                if (deleted) {
                    log.debug("已清理用户提醒列表缓存, userId: {}, key: {}", userId, listCacheKey);
                }
            } catch (Exception e) {
                log.warn("清理用户提醒列表缓存失败, userId: {}, error: {}", userId, e.getMessage());
            }
    
            // 清理提醒统计缓存
            String statCacheKey = CacheConstants.REMIND_STAT_KEY + ":" + userId;
            try {
                boolean deleted = redisCache.deleteObject(statCacheKey);
                if (deleted) {
                    log.debug("已清理用户提醒统计缓存, userId: {}, key: {}", userId, statCacheKey);
                }
            } catch (Exception e) {
                log.warn("清理用户提醒统计缓存失败, userId: {}, error: {}", userId, e.getMessage());
            }


        } catch (BaseException e) {
            log.error("更新用户货币失败: userId={}, currency={}, error={}", userId, currency, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("更新用户货币时发生未知错误: userId={}, currency={}", userId, currency, e);
            throw new BaseException(-500, "更新货币设置失败");
        }
    }

    /**
     * 更新用户所有提醒记录的汇率
     *
     * @param userId 用户ID
     * @param oldCurrency 原货币代码
     * @param newCurrency 新货币代码
     */
    public void updateUserRemindRates(String userId, String oldCurrency, String newCurrency) {
        try {
            // 查询用户所有提醒记录
            FgRemindPo queryRemind = new FgRemindPo();
            queryRemind.setUserId(userId);
            List<FgRemindPo> remindList = fgRemindDao.filterFgRemindList(queryRemind);

            if (remindList == null || remindList.isEmpty()) {
                log.info("用户 {} 没有提醒记录需要更新", userId);
                return;
            }

            log.info("开始更新用户 {} 的 {} 条提醒记录汇率", userId, remindList.size());

            int updatedCount = 0;
            for (FgRemindPo remind : remindList) {
                if (updateSingleRemindRate(remind, oldCurrency, newCurrency)) {
                    updatedCount++;
                }
            }

            // 更新提醒信息记录的汇率
            updateUserRemindInfoRates(userId, oldCurrency, newCurrency);

            log.info("成功更新用户 {} 的 {} 条提醒记录汇率", userId, updatedCount);

        } catch (Exception e) {
            log.error("更新用户提醒记录汇率失败: userId={}, oldCurrency={}, newCurrency={}, error={}",
                userId, oldCurrency, newCurrency, e.getMessage());
            throw new RuntimeException("更新提醒记录汇率失败", e);
        }
    }

    /**
     * 更新单个提醒记录的汇率
     *
     * @param remind 提醒记录
     * @param oldCurrency 原货币代码
     * @param newCurrency 新货币代码
     * @return 是否更新成功
     */
    private boolean updateSingleRemindRate(FgRemindPo remind, String oldCurrency, String newCurrency) {
        try {
            String remindCurrency = remind.getCurrency();
            if (StringUtils.isEmpty(remindCurrency)) {
                log.warn("提醒记录 {} 的货币代码为空，跳过更新", remind.getId());
                return false;
            }

            // 计算新汇率：从提醒记录的货币转换到用户新设置的货币
            BigDecimal newRate = currencyConversionUtil.getConversionRate(remindCurrency, newCurrency);
            if (newRate == null) {
                log.warn("无法获取汇率: {} -> {}，跳过提醒记录 {}", remindCurrency, newCurrency, remind.getId());
                return false;
            }

            // 更新提醒记录的汇率
            FgRemindPo updateRemind = new FgRemindPo();
            updateRemind.setId(remind.getId());
            updateRemind.setRate(newRate);
            updateRemind.setUpdateTime(TimeUtils.getCurrentTime());

            int result = fgRemindDao.updateFgRemind(updateRemind);
            if (result > 0) {
                log.debug("更新提醒记录 {} 汇率: {} -> {} ({})",
                    remind.getId(), remind.getRate(), newRate, remindCurrency + "->" + newCurrency);
                return true;
            } else {
                log.warn("更新提醒记录 {} 汇率失败", remind.getId());
                return false;
            }

        } catch (Exception e) {
            log.error("更新提醒记录 {} 汇率时发生错误: {}", remind.getId(), e.getMessage());
            return false;
        }
    }

    /**
     * 更新用户所有提醒信息记录的汇率
     *
     * @param userId 用户ID
     * @param oldCurrency 原货币代码
     * @param newCurrency 新货币代码
     */
    private void updateUserRemindInfoRates(String userId, String oldCurrency, String newCurrency) {
        try {
            // 查询用户所有提醒信息记录
            FgRemindInfoPo queryRemindInfo = new FgRemindInfoPo();
            queryRemindInfo.setUserId(userId);
            List<FgRemindInfoPo> remindInfoList = fgRemindInfoDao.filterFgRemindInfoList(queryRemindInfo);

            if (remindInfoList == null || remindInfoList.isEmpty()) {
                log.info("用户 {} 没有提醒信息记录需要更新", userId);
                return;
            }

            log.info("开始更新用户 {} 的 {} 条提醒信息记录汇率", userId, remindInfoList.size());

            int updatedCount = 0;
            for (FgRemindInfoPo remindInfo : remindInfoList) {
                if (updateSingleRemindInfoRate(remindInfo, oldCurrency, newCurrency)) {
                    updatedCount++;
                }
            }

            log.info("成功更新用户 {} 的 {} 条提醒信息记录汇率", userId, updatedCount);

        } catch (Exception e) {
            log.error("更新用户提醒信息记录汇率失败: userId={}, error={}", userId, e.getMessage());
            throw new RuntimeException("更新提醒信息记录汇率失败", e);
        }
    }

    /**
     * 更新单个提醒信息记录的汇率
     *
     * @param remindInfo 提醒信息记录
     * @param oldCurrency 原货币代码
     * @param newCurrency 新货币代码
     * @return 是否更新成功
     */
    private boolean updateSingleRemindInfoRate(FgRemindInfoPo remindInfo, String oldCurrency, String newCurrency) {
        try {
            String remindInfoCurrency = remindInfo.getCurrency();
            if (StringUtils.isEmpty(remindInfoCurrency)) {
                log.warn("提醒信息记录 {} 的货币代码为空，跳过更新", remindInfo.getId());
                return false;
            }

            // 计算新汇率：从提醒信息记录的货币转换到用户新设置的货币
            BigDecimal newRate = currencyConversionUtil.getConversionRate(remindInfoCurrency, newCurrency);
            if (newRate == null) {
                log.warn("无法获取汇率: {} -> {}，跳过提醒信息记录 {}", remindInfoCurrency, newCurrency, remindInfo.getId());
                return false;
            }

            int result = fgRemindInfoDao.updateFgRemindInfoRate(newRate, remindInfo.getUserId(), remindInfo.getId());
            if (result > 0) {
                log.debug("更新提醒信息记录 {} 汇率: {} -> {} ({})",
                    remindInfo.getId(), remindInfo.getRate(), newRate, remindInfoCurrency + "->" + newCurrency);
                return true;
            } else {
                log.warn("更新提醒信息记录 {} 汇率失败", remindInfo.getId());
                return false;
            }

        } catch (Exception e) {
            log.error("更新提醒信息记录 {} 汇率时发生错误: {}", remindInfo.getId(), e.getMessage());
            return false;
        }
    }
}

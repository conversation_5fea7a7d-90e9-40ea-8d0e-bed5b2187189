package com.sub.subm.api.controller.v1;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.sub.common.annotation.DataI18n;
import com.sub.common.constant.CacheConstants;
import com.sub.common.core.common.CommonResult;
import com.sub.common.core.redis.RedisCache;
import com.sub.subm.api.service.ExchangRateService;
import com.sub.subm.api.service.PrefixJoinServiceImpl;
import com.sub.subm.api.service.ProjectServiceImpl;
import com.sub.subm.dal.entity.generate.product.ProductPlanPo;

import io.swagger.v3.oas.annotations.tags.Tag;



@RestController
@RequestMapping("/api/v1/")
@Tag(name = "项目初始化需获取的数据")
public class ProjectController extends CommonResult {

    private static final Logger log = LoggerFactory.getLogger(ProjectController.class);
    private static final int INIT_CACHE_TIMEOUT = 60; // 初始化数据缓存60分钟
    private static final int PLAN_CACHE_TIMEOUT = 30; // 产品计划缓存30分钟

    @Autowired
    private ProjectServiceImpl projectService;
    @Autowired
    private ExchangRateService exchangRateService;
    @Autowired
    private PrefixJoinServiceImpl prefixJoinService;
    
    @Autowired
    private RedisCache redisCache;

    @DataI18n
    @RequestMapping(value = "init", method = RequestMethod.GET)
    public CommonResult init() {
        // String cacheKey = CacheConstants.PROJECT_INIT_KEY;
        
        // // 尝试从缓存获取数据
        // try {
        //     @SuppressWarnings("unchecked")
        //     Map<String, Object> cachedData = redisCache.getCacheObject(cacheKey);
        //     if (cachedData != null) {
        //         log.debug("从缓存获取项目初始化数据, key: {}", cacheKey);
        //         return ok(cachedData);
        //     }
        // } catch (Exception e) {
        //     log.warn("获取项目初始化缓存失败: {}", e.getMessage());
        // }
        
        // 缓存未命中，执行原有业务逻辑
        Map<String, Object> initMap = new HashMap<>();
        initMap.put("regionList", prefixJoinService.prefixImageRegion(projectService.filterRegionList()));
        initMap.put("productList", prefixJoinService.prefixImageProduct(projectService.filterProductList()));
        initMap.put("categoryList", prefixJoinService.prefixImageCategory(projectService.getAllProductCategoryList()));
        initMap.put("currencyList", exchangRateService.getDbCurrencyList());
        
        // // 将结果存储到缓存
        // try {
        //     redisCache.setCacheObject(cacheKey, initMap, INIT_CACHE_TIMEOUT, TimeUnit.MINUTES);
        //     log.debug("项目初始化数据已缓存, key: {}", cacheKey);
        // } catch (Exception e) {
        //     log.warn("缓存项目初始化数据失败: {}", e.getMessage());
        // }
        
        return ok(initMap);
    }



    @RequestMapping(value = "productPlan/{productId}", method = RequestMethod.GET)
    public CommonResult productPlan(@PathVariable("productId") String productId) {
        String cacheKey = CacheConstants.PROJECT_PLAN_KEY + ":" + productId;
        
        // 尝试从缓存获取数据
        try {
            @SuppressWarnings("unchecked")
            Map<String, List<ProductPlanPo>> cachedData = redisCache.getCacheObject(cacheKey);
            if (cachedData != null) {
                log.debug("从缓存获取产品计划数据, key: {}", cacheKey);
                return ok(cachedData);
            }
        } catch (Exception e) {
            log.warn("获取产品计划缓存失败: {}", e.getMessage());
        }
        
        // 缓存未命中，执行原有业务逻辑
        ProductPlanPo productPlanPo = new ProductPlanPo();
        productPlanPo.setProductId(productId);
        List<ProductPlanPo> planPoList = projectService.filterProductPlanList(productPlanPo);
        Map<String, List<ProductPlanPo>> map = planPoList.stream().collect(Collectors.groupingBy(ProductPlanPo::getRegionId));
        
        // 将结果存储到缓存
        try {
            redisCache.setCacheObject(cacheKey, map, PLAN_CACHE_TIMEOUT, TimeUnit.MINUTES);
            log.debug("产品计划数据已缓存, key: {}", cacheKey);
        } catch (Exception e) {
            log.warn("缓存产品计划数据失败: {}", e.getMessage());
        }
        
        return ok(map);
    }

}

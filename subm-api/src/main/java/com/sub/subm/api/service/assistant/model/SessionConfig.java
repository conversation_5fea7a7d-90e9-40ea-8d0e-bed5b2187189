package com.sub.subm.api.service.assistant.model;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;

/**
 * 会话配置模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SessionConfig implements Serializable {
    
    /**
     * 会话超时时间（分钟）
     */
    @Builder.Default
    private Integer sessionTimeoutMinutes = 30;
    
    /**
     * 最大消息历史数量
     */
    @Builder.Default
    private Integer maxMessageHistory = 20;
    
    /**
     * 是否启用流式响应
     */
    @Builder.Default
    private Boolean enableStreaming = true;
    
    /**
     * 是否启用工具调用
     */
    @Builder.Default
    private Boolean enableTools = true;
    
    /**
     * 模型温度参数
     */
    @Builder.Default
    private Double temperature = 0.7;
    
    /**
     * 最大输出令牌数
     */
    @Builder.Default
    private Integer maxTokens = 2000;
    
    /**
     * 是否记录对话历史
     */
    @Builder.Default
    private Boolean logConversation = true;
    
    /**
     * 语言设置
     */
    @Builder.Default
    private String language = "zh-CN";
}

package com.sub.subm.api.controller.v1;

import java.io.File;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.sub.common.constant.CacheConstants;
import com.sub.common.core.common.CommonResult;
import com.sub.common.exception.BaseException;
import com.sub.common.utils.StringUtils;
import com.sub.common.utils.auth.AuthUtils;
import com.sub.subm.api.entity.dto.UserLoginDTOs;
import com.sub.subm.api.security.SecurityUtils;
import com.sub.subm.api.service.UserServiceImpl;
import com.sub.subm.api.service.account.VerifyCodeService;
import com.sub.subm.dal.entity.generate.user.UserPo;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@RequestMapping(value = "/api/v1/user")
@RestController
@Tag(name = "用户模块")
public class UserController extends CommonResult {
    // 使用项目根目录下的uploads文件夹
    private String getUploadPath() {
        String projectRoot = System.getProperty("user.dir");
        return projectRoot + "/uploads/";
    }

    @Autowired
    private UserServiceImpl userService;
    @Autowired
    private VerifyCodeService verifyCodeService;

    @Operation(summary = "用户修改密码, 记得旧密码情况下")
    @RequestMapping(value = "updatePwd", method = RequestMethod.POST)
    public CommonResult updatePwd(@RequestBody @Validated UserLoginDTOs.UpdateAccountPwd data) throws BaseException {
        String userId = SecurityUtils.getUserId();
        UserPo userPo = userService.getByIdUserPo(userId);
        // 密码不为空,则需要验证旧密码
        if (StringUtils.isNotEmpty(userPo.getPassword())) {
            String salt = userPo.getSalt();
            if (!AuthUtils.md5Hex(data.getOldPwd(), salt).equals(userPo.getPassword())) {
                throw new BaseException(-300, "old.password.error");
            }
        }
        UserPo newUserPo = new UserPo();
        newUserPo.setId(userId);
        newUserPo.setSalt(StringUtils.verifySalt(6));
        newUserPo.setPassword(AuthUtils.md5Hex(data.getNewPwd(), newUserPo.getSalt()));
        userService.updateUser(newUserPo);
        return ok();
    }

    @Operation(summary = "用户修改密码, 忘记密码情况, 使用验证码进行, 注意先请求发送验证码接口")
    @RequestMapping(value = "updateVerifyPwd", method = RequestMethod.POST)
    public CommonResult updateVerifyPwd(@RequestBody @Validated UserLoginDTOs.ForPwdDTO data) throws BaseException {
        String userId = SecurityUtils.getUserId();
        UserPo userPo = userService.getByIdUserPo(userId);
        verifyCodeService.verifyTypeAccount(userPo, VerifyCodeService.MailSubject.UPDATE.ordinal());
        // 验证key 是否正确
        String redisKey = CacheConstants.VERIFY + data.getChannel() + ":" +
                VerifyCodeService.MailSubject.FORGET.ordinal() + ":"+ data.getAccount();

        verifyCodeService.verifyCode(redisKey, data.getVerifyCode());
        UserPo newUserPo = new UserPo();
        newUserPo.setId(userId);
        newUserPo.setSalt(StringUtils.verifySalt(6));
        newUserPo.setPassword(AuthUtils.md5Hex(data.getPwd(), newUserPo.getSalt()));
        userService.updateUser(userPo);
        return ok();
    }

    private static final long MAX_FILE_SIZE = 5 * 1024 * 1024;
    private static final List<String> FILES_FORMAT = Arrays.asList("png", "jpg", "jpeg");
    @Operation(summary = "上传头像")
    @RequestMapping(value = "uploadAvatar", method = RequestMethod.POST)
    public CommonResult uploadAvatar(@RequestParam("file") MultipartFile file) throws BaseException {
        String userId = SecurityUtils.getUserId();
        UserPo userPo = userService.getByIdUserPo(userId);
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new BaseException(-400, "file.size.limit");
        }
        String fileExtension = FilenameUtils.getExtension(file.getOriginalFilename()).toLowerCase();
        if (!FILES_FORMAT.contains(fileExtension)) {
            throw new BaseException(-400, "file.format");
        }
        String imgPath =  "avatar" + File.separator + userId + File.separator +
                 + System.currentTimeMillis() + "." + fileExtension;
        Path newFilePath  = Paths.get(getUploadPath(), imgPath);
        try {
            Files.createDirectories(newFilePath.getParent());
            // 将文件内容写入到指定路径
            Files.copy(file.getInputStream(), newFilePath, StandardCopyOption.REPLACE_EXISTING);
        } catch (Exception e) {
            throw new BaseException(-400, "upload.file.error");
        }
        userPo.setId(userId);
        String saveImg = imgPath.replace("\\", "/");
        try {
            URLEncoder.encode(imgPath, "UTF-8");
        } catch (Exception e) {
        }
        userPo.setAvatar("/" + saveImg);
        userService.updateUser(userPo);
        return ok();
    }

    @Operation(summary = "修改用户信息")
    @RequestMapping(value = "updateUserInfo", method = RequestMethod.POST)
    public CommonResult updateUserInfo(@RequestBody @Validated UserLoginDTOs.UserBasicInfo data) throws BaseException {
        String userId = SecurityUtils.getUserId();
        UserPo newUserPo = new UserPo();
        newUserPo.setId(userId);
        BeanUtils.copyProperties(data, newUserPo);
        userService.updateUser(newUserPo);
        return ok();
    }

    @Operation(summary = "绑定账号, 适用用于三方")
    @RequestMapping(value = "bindAccount", method = RequestMethod.POST)
    public CommonResult bindAccount(@RequestBody @Validated UserLoginDTOs.BindAccountDTO data) throws BaseException {
        verifyCodeService.verifyChannelOpen(data.getChannel(), data.getAccount());
        String userId = SecurityUtils.getUserId();
        if (data.getChannel() == 1 || data.getChannel() == 2) {
            String redisKey = CacheConstants.VERIFY  +
                    data.getChannel() + ":" + VerifyCodeService.MailSubject.BINDING.ordinal() + ":" + data.getAccount();
            verifyCodeService.verifyCode(redisKey, data.getVerifyCode());
        }
        UserPo userPo = new UserPo();
        userPo.setId(userId);
        userPo = UserPo.buildChannelUserPo(userPo, data.getUuid(), data.getAccount(), data.getChannel());
        userService.updateUser(userPo);
        return ok();
    }

    @Operation(summary = "解绑账号, 适用于三方登录")
    @RequestMapping(value = "unBindAccount", method = RequestMethod.POST)
    public CommonResult unBindAccount(@RequestBody @Validated UserLoginDTOs.BindAccountDTO data) throws BaseException {
        verifyCodeService.verifyChannelOpen(data.getChannel(), data.getAccount());
        String userId = SecurityUtils.getUserId();
        if (data.getChannel() == 1 || data.getChannel() == 2) {
            String redisKey = CacheConstants.VERIFY  +
                    data.getChannel() + ":" + VerifyCodeService.MailSubject.BINDING.ordinal() + ":" + data.getAccount();
            verifyCodeService.verifyCode(redisKey, data.getVerifyCode());
        }
        UserPo userPo = new UserPo();
        userPo.setId(userId);
        userPo = UserPo.unBindChannelAccount(userPo, data.getChannel());
        userService.updateUser(userPo);
        return ok();
    }

    @Operation(summary = "登出登录")
    @RequestMapping(value = "logout", method = RequestMethod.GET)
    public CommonResult logout() throws BaseException {
        return CommonResult.ok(100, "logout.login.success");
    }


    @Operation(summary = "注销")
    @RequestMapping(value = "unsubscribeAccount", method = RequestMethod.POST)
    public CommonResult unsubscribeAccount() throws BaseException {
        String userId = SecurityUtils.getUserId();
        userService.unsubscribeAccount(userId);
        return CommonResult.ok();
    }

    /**
     * 用户更改货币选项
     */
    @Operation(summary = "用户更改货币")
    @RequestMapping(value = "updateCurrency", method = RequestMethod.GET)
    public CommonResult updateCurrency(@RequestParam("currency") String currency) throws BaseException {
        String userId = SecurityUtils.getUserId();
        userService.updateCurrency(userId, currency);
        return CommonResult.ok();
    }
}

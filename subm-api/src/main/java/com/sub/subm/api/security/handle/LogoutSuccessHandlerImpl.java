package com.sub.subm.api.security.handle;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.web.servlet.HandlerExceptionResolver;

import com.alibaba.fastjson2.JSON;
import com.sub.common.core.common.CommonResult;
import com.sub.common.exception.BaseException;
import com.sub.common.utils.MessageUtils;
import com.sub.common.utils.ServletUtils;
import com.sub.common.utils.StringUtils;
import com.sub.framework.jwt.exception.LoginTokenEnum;
import com.sub.subm.api.entity.vo.LoginUser;
import com.sub.subm.api.security.TokenUtil;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;


/**
 * 自定义退出处理类 返回成功
 */
@Configuration
public class LogoutSuccessHandlerImpl implements LogoutSuccessHandler {
    @Autowired
    private TokenUtil tokenUtil;
    @Autowired
    @Qualifier("handlerExceptionResolver")
    private HandlerExceptionResolver resolver;

    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) {
        LoginUser loginUser = tokenUtil.getToken(request);
        if (StringUtils.isNull(loginUser)) {
            ServletUtils.renderString(response, JSON.toJSONString(CommonResult.ok(-1, MessageUtils.message("invalid.handle"))));
            return;
        }
        try {
            LoginTokenEnum  logout = tokenUtil.logout(loginUser.getOs(), loginUser.getUserId());
            ServletUtils.renderString(response, JSON.toJSONString(CommonResult.ok(100, MessageUtils.message(logout.getMessage()))));
        } catch (BaseException e) {
            resolver.resolveException(request, response, null, e);
            return;
        }

    }
}

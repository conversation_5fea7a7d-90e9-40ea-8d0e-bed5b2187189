package com.sub.subm.api.service.assistant.model;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.stereotype.Component;

import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.store.memory.chat.ChatMemoryStore;
import lombok.extern.slf4j.Slf4j;

/**
 * 持久化聊天记忆存储实现
 * 支持多用户/多会话的聊天记忆管理
 */
@Slf4j
@Component
public class PersistentChatMemoryStore implements ChatMemoryStore {

    // 使用ConcurrentHashMap确保线程安全
    private final Map<Object, List<ChatMessage>> chatMemoryMap = new ConcurrentHashMap<>();

    @Override
    public List<ChatMessage> getMessages(Object memoryId) {
        log.debug("获取聊天记忆, memoryId: {}", memoryId);

        // 如果不存在则返回空列表，而不是null
        List<ChatMessage> messages = chatMemoryMap.get(memoryId);
        if (messages == null) {
            log.debug("memoryId {} 没有历史消息，返回空列表", memoryId);
            return new ArrayList<>();
        }

        log.debug("memoryId {} 找到 {} 条历史消息", memoryId, messages.size());
        return new ArrayList<>(messages); // 返回副本以避免外部修改
    }

    @Override
    public void updateMessages(Object memoryId, List<ChatMessage> messages) {
        log.debug("更新聊天记忆, memoryId: {}, 消息数量: {}", memoryId, messages != null ? messages.size() : 0);

        if (messages == null) {
            chatMemoryMap.remove(memoryId);
        } else {
            // 存储消息的副本
            chatMemoryMap.put(memoryId, new ArrayList<>(messages));
        }
    }

    @Override
    public void deleteMessages(Object memoryId) {
        log.debug("删除聊天记忆, memoryId: {}", memoryId);
        List<ChatMessage> removed = chatMemoryMap.remove(memoryId);
        if (removed != null) {
            log.info("成功删除memoryId {} 的 {} 条消息", memoryId, removed.size());
        }
    }

    /**
     * 获取当前存储的会话数量
     */
    public int getSessionCount() {
        return chatMemoryMap.size();
    }

    /**
     * 清空所有聊天记忆
     */
    public void clearAll() {
        int count = chatMemoryMap.size();
        chatMemoryMap.clear();
        log.info("清空所有聊天记忆，共清理 {} 个会话", count);
    }
}

package com.sub.subm.api.security;



import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import com.sub.common.exception.BaseException;
import com.sub.subm.api.entity.vo.LoginUser;

/**
 * 安全服务工具类
 */
@Slf4j
public class SecurityUtils {

    /**
     * 获取用id
     */
    public static String getUserId() throws BaseException {
        try {
            return getLoginUser().getUserId();
        } catch (Exception e) {
            throw new BaseException(HttpStatus.UNAUTHORIZED.value(), "获取用户ID异常");
        }
    }
    /**
     * 获取用id, 主动拦截异常
     */
    public static String getUserIdNotE() {
        try {
            return getLoginUser().getUserId();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     *  获取 用户 角色 id
     * @return
     */
    public static String getRole() throws BaseException {
        try {
            return getLoginUser().getRoleId();
        } catch (Exception e) {
            throw new BaseException(HttpStatus.UNAUTHORIZED.value(), "获取用户角色id异常");
        }

    }

    /**
     * 获取用户
     **/
    public static LoginUser getLoginUser() throws BaseException {
        try {
            return (LoginUser) getAuthentication().getPrincipal();
        } catch (Exception e) {
            throw new BaseException(HttpStatus.UNAUTHORIZED.value(), "获取用户信息异常");
        }
    }

    /**
     * 获取Authentication
     */
    public static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }
}

package com.sub.subm.api.service.assistant.tools;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.sub.common.exception.BaseException;
import com.sub.common.utils.TimeUtils;
import com.sub.subm.api.entity.vo.filterRemindVO;
import com.sub.subm.api.service.PrefixJoinServiceImpl;
import com.sub.subm.api.service.ProjectServiceImpl;
import com.sub.subm.api.service.RemindInfoServiceImpl;
import com.sub.subm.api.service.RemindServiceImpl;
import com.sub.subm.api.service.assistant.context.UserContextManager;
import com.sub.subm.api.service.assistant.model.dto.ToolCallResponse;
import com.sub.subm.dal.entity.generate.remind.FgRemindInfoPo;
import com.sub.subm.dal.entity.generate.remind.FgRemindPo;

import cn.hutool.core.util.StrUtil;
import dev.langchain4j.agent.tool.P;
import dev.langchain4j.agent.tool.Tool;
import dev.langchain4j.agent.tool.ToolMemoryId;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 提醒管理工具类
 * 基于FgRemindController的功能实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RemindTool {

    private final RemindServiceImpl remindService;
    private final RemindInfoServiceImpl remindInfoService;
    private final PrefixJoinServiceImpl prefixJoinService;
    private final UserContextManager userContextManager;
    private final ProjectServiceImpl projectService;

    @Tool("查询当前用户的订阅提醒列表")
    public ToolCallResponse getRemindList(
            @ToolMemoryId Object memoryId) {
        long startTime = System.currentTimeMillis();
        try {
            userContextManager.validateUser(memoryId);
            String userId = userContextManager.getUserId(memoryId);
            log.info("查询用户提醒列表, userId: {}", userId);

            FgRemindPo fgRemindPo = new FgRemindPo();
            fgRemindPo.setUserId(userId);
            List<filterRemindVO> list = remindService.filterFgRemindList(fgRemindPo);

            if (list == null || list.isEmpty()) {
                return ToolCallResponse.builder()
                        .toolName("getRemindList")
                        .toolResult("您当前没有任何订阅提醒记录。")
                        .toolResultDescription("查询提醒列表结果为空")
                        .toolStatus("success")
                        .executionTime(System.currentTimeMillis() - startTime)
                        .toolVersion("1.0")
                        .build();
            }

            // 处理图片前缀
            list = prefixJoinService.prefixImgRemind(list);

            return ToolCallResponse.builder()
                    .toolName("getRemindList")
                    .toolResult(list)
                    .toolResultDescription(String.format("成功查询到 %d 条提醒记录", list.size()))
                    .toolStatus("success")
                    .executionTime(System.currentTimeMillis() - startTime)
                    .toolVersion("1.0")
                    .build();

        } catch (Exception e) {
            log.error("查询提醒列表失败", e);
            return ToolCallResponse.builder()
                    .toolName("getRemindList")
                    .toolResult("查询提醒列表失败: " + e.getMessage())
                    .toolResultDescription("查询提醒列表时发生错误")
                    .toolStatus("failed")
                    .executionTime(System.currentTimeMillis() - startTime)
                    .errorMessage(e.getMessage())
                    .toolVersion("1.0")
                    .build();
        }
    }

    @Tool("查询当前用户的订阅费用统计，包括总金额、按时间分组（按月统计或按年统计）和按类别分组的统计数据")
    public ToolCallResponse filterStat(
            @ToolMemoryId Object memoryId,
            @P("时间分组类型：month或year") String date) {
        Long executionStartTime = System.currentTimeMillis();
        try {
            userContextManager.validateUser(memoryId);
            String userId = userContextManager.getUserId(memoryId);
            Long startTime = TimeUtils.getMonthStartTime();
            Long endTime = TimeUtils.getMonthEndTime();
            if ("year".equals(date)) {
                startTime = TimeUtils.getYearStartTime();
                endTime = TimeUtils.getYearEndTime();
            }

            FgRemindInfoPo remindInfoPo = new FgRemindInfoPo();
            remindInfoPo.setUserId(userId);
            remindInfoPo.setStartTime(startTime);
            remindInfoPo.setEndTime(endTime);

            Map<String, Object> statics = new HashMap<>();

            // 1. 计算总金额
            statics.put("total", remindInfoService.totalAmount(remindInfoPo));

            // 2. 按时间分组统计
            Map<String, Object> dateParams = new HashMap<>();
            dateParams.put("groupBy", date);
            remindInfoPo.setParams(dateParams);
            statics.put("date", remindInfoService.filterFgRemindInfoList(remindInfoPo));

            // 3. 按类别分组统计
            Map<String, Object> categoryParams = new HashMap<>();
            categoryParams.put("groupBy", "category");
            remindInfoPo.setParams(categoryParams);
            statics.put("category", remindInfoService.filterFgRemindInfoList(remindInfoPo));

            return ToolCallResponse.builder()
                    .toolName("filterStat")
                    .toolResult(statics)
                    .toolResultDescription(String.format("提醒费用统计 - 总金额:¥%.2f, 时间分组:%s, 类别分组统计",
                            (BigDecimal) statics.get("total"), date))
                    .toolStatus("success")
                    .executionTime(System.currentTimeMillis() - executionStartTime)
                    .toolVersion("1.0")
                    .build();

        } catch (Exception e) {
            log.error("查询提醒统计失败", e);
            return ToolCallResponse.builder()
                    .toolName("filterStat")
                    .toolResult("查询提醒统计失败: " + e.getMessage())
                    .toolResultDescription("查询提醒统计时发生错误")
                    .toolStatus("failed")
                    .executionTime(System.currentTimeMillis() - executionStartTime)
                    .errorMessage(e.getMessage())
                    .toolVersion("1.0")
                    .build();
        }
    }

    @Tool("获取今天的日期")
    public ToolCallResponse getTodayDate() {
        return ToolCallResponse.builder()
                .toolName("getTodayDate")
                .toolResult(LocalDate.now())
                .toolResultDescription("获取今天的日期")
                .toolStatus("success")
                .executionTime(System.currentTimeMillis())
                .toolVersion("1.0")
                .build();
    }

    @Tool("将时间戳转换为日期")
    public ToolCallResponse convertTimestampToDate(
            @P("时间戳") Long timestamp) {
        return ToolCallResponse.builder()
                .toolName("convertTimestampToDate")
                .toolResult(LocalDate.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault()))
                .toolResultDescription("将时间戳转换为日期")
                .toolStatus("success")
                .executionTime(System.currentTimeMillis())
                .toolVersion("1.0")
                .build();
    }

    @Tool("查询系统分类类表")
    public ToolCallResponse getCategoryList() {
        return ToolCallResponse.builder()
                .toolName("getCategoryList")
                .toolResult(projectService.getAllProductCategoryList())
                .toolResultDescription("查询系统分类列表")
                .toolStatus("success")
                .executionTime(System.currentTimeMillis())
                .toolVersion("1.0")
                .build();
    }

    @Tool("""
            为当前用户添加新的订阅提醒。执行步骤：
            1. 首先询问用户是否要添加自定义订阅提醒（目前仅支持自定义订阅）
            2. 调用getTodayDate获取今天的日期作为参考
            3. 根据用户描述确定开始时间：
               - 如果用户明确提到具体日期，使用该日期作为开始时间
               - 如果用户只描述相对时间（如"下个月"、"明年"），基于今天日期计算开始时间
               - 如果用户未提及时间，默认使用今天作为开始时间
            4. 根据用户描述确定结束时间：
               - 如果用户明确提到具体日期，使用该日期作为结束时间
               - 如果用户只描述相对时间（如"下个月"、"明年"），基于今天日期计算结束时间
               - 如果用户未提及时间，结束时间默认为null
            5. 收集必要信息：产品名称、套餐名称、价格等
            6. 调用getCategoryList获取系统分类列表，判断用户添加的订阅类型是否在系统分类列表中，如果不在默认为null，如果在则填入英文分类name
            7. 可选信息：货币类型、提醒周期、描述等
            """)
    public ToolCallResponse addRemind(
            @ToolMemoryId Object memoryId,
            @P("产品名称") String productName,
            @P("套餐名称") String planName,
            @P("价格") String amount,
            @P(value = "开始时间",required = false) LocalDate startTime,
            @P(value = "结束时间",required = false) LocalDate endTime,
            @P(value = "货币类型，如CNY、USD", required = false) String currency,
            @P(value = "提醒周期（0-永久，1-每年，3-每月，4-每周，5-每日）", required = false) String remindCycle,
            @P(value = "描述", required = false) String desc,
            @P(value = "是否为自定义订阅提醒：0-否，1-是") String isCustom,
            @P(value = "种类", required = false) String category) {
        long executionStartTime = System.currentTimeMillis();
        try {
            userContextManager.validateUser(memoryId);
            String userId = userContextManager.getUserId(memoryId);
            if (isCustom.equals("0")) {
                // TODO 先暂时不可添加非自定义订阅
                throw new BaseException("暂不支持添加非自定义订阅");
            }

            FgRemindPo fgRemindPo = new FgRemindPo();
            fgRemindPo.setUserId(userId);
            fgRemindPo.setProductName(productName);
            fgRemindPo.setPlanName(planName);
            fgRemindPo.setAmount(new BigDecimal(amount));
            fgRemindPo.setStatus(1);
            fgRemindPo.setCreateTime(TimeUtils.getCurrentTime());
            
            if (currency == null) {
                fgRemindPo.setCurrency(userContextManager.getUserInfo(memoryId).getCurrency());
            } else {
                fgRemindPo.setCurrency(currency);
            }

            fgRemindPo = remindService.getByProductId(fgRemindPo);
            
            if (remindCycle == null) {
                fgRemindPo.setRemindCycle(0);
            } else {
                fgRemindPo.setRemindCycle(Integer.valueOf(remindCycle));
            }
            if (startTime == null) {
                fgRemindPo.setStartTime(TimeUtils.getCurrentTime());
            } else {
                fgRemindPo.setStartTime(startTime.atStartOfDay(ZoneId.systemDefault()).toEpochSecond());
            }
            if (endTime == null) {
                fgRemindPo.setEndTime(null);
            } else {
                fgRemindPo.setEndTime(endTime.atStartOfDay(ZoneId.systemDefault()).toEpochSecond());
            }
            if (desc != null)
                fgRemindPo.setDesc(desc);
            if (isCustom != null)
                fgRemindPo.setIsCustom(Integer.valueOf(isCustom));
            if (category != null)
                fgRemindPo.setCategory(category);

            // 获取产品信息和汇率(自定义不需要)
            // fgRemindPo = remindService.getByProductId(fgRemindPo);

            // 插入提醒记录
            remindService.insertFgRemindPo(fgRemindPo);

            return ToolCallResponse.builder()
                    .toolName("addRemind")
                    .toolResult(fgRemindPo)
                    .toolResultDescription(String.format("成功添加订阅提醒: %s", productName))
                    .toolStatus("success")
                    .executionTime(System.currentTimeMillis() - executionStartTime)
                    .toolVersion("1.0")
                    .build();

        } catch (Exception e) {
            log.error("添加提醒失败", e);
            return ToolCallResponse.builder()
                    .toolName("addRemind")
                    .toolResult("添加提醒失败: " + e.getMessage())
                    .toolResultDescription("添加提醒时发生错误")
                    .toolStatus("failed")
                    .executionTime(System.currentTimeMillis() - executionStartTime)
                    .errorMessage(e.getMessage())
                    .toolVersion("1.0")
                    .build();
        }
    }

    // @Tool("更新指定订阅提醒信息")
    // public ToolCallResponse updateRemind(
    // @ToolMemoryId Object memoryId,
    // @P("提醒ID") String remindId,
    // @P(value = "产品名称", required = false) String productName,
    // @P(value = "套餐名称", required = false) String planName,
    // @P(value = "价格", required = false) String amount,
    // @P(value = "货币类型", required = false) String currency,
    // @P(value = "描述", required = false) String desc,
    // @P(value = "状态：1-活跃，0-已过期", required = false) String status) {
    // long executionStartTime = System.currentTimeMillis();
    // try {
    // userContextManager.validateUser(memoryId);
    // String userId = userContextManager.getUserId(memoryId);
    // log.info("更新用户提醒, userId: {}, remindId: {}", userId, remindId);

    // FgRemindPo fgRemindPo = new FgRemindPo();
    // fgRemindPo.setId(remindId);
    // fgRemindPo.setUserId(userId);
    // fgRemindPo.setUpdateTime(TimeUtils.getCurrentTime());

    // // 设置要更新的字段
    // if (productName != null) fgRemindPo.setProductName(productName);
    // if (planName != null) fgRemindPo.setPlanName(planName);
    // if (amount != null) fgRemindPo.setAmount(new BigDecimal(amount));
    // if (currency != null) fgRemindPo.setCurrency(currency);
    // if (desc != null) fgRemindPo.setDesc(desc);
    // if (status != null) fgRemindPo.setStatus(Integer.valueOf(status));

    // remindService.updateFgRemindPo(fgRemindPo);

    // return ToolCallResponse.builder()
    // .toolName("updateRemind")
    // .toolResult(fgRemindPo)
    // .toolResultDescription(String.format("成功更新提醒ID: %s", remindId))
    // .toolStatus("success")
    // .executionTime(System.currentTimeMillis() - executionStartTime)
    // .toolVersion("1.0")
    // .build();

    // } catch (Exception e) {
    // log.error("更新提醒失败", e);
    // return ToolCallResponse.builder()
    // .toolName("updateRemind")
    // .toolResult("更新提醒失败: " + e.getMessage())
    // .toolResultDescription("更新提醒时发生错误")
    // .toolStatus("failed")
    // .executionTime(System.currentTimeMillis() - executionStartTime)
    // .errorMessage(e.getMessage())
    // .toolVersion("1.0")
    // .build();
    // }
    // }

    @Tool("""
        删除指定的订阅提醒记录。执行步骤：
        1. 必须先调用"查询当前用户的订阅提醒列表"工具获取用户的所有订阅提醒
        2. 向用户展示当前的订阅提醒列表，包括产品名称、套餐名称、价格、状态等信息
        3. 要求用户明确指定要删除的订阅提醒的产品名称（必须与列表中的产品名称完全匹配）
        4. 确认删除操作前，向用户说明将要删除的订阅提醒详情
        5. 执行删除操作，同时删除提醒记录和相关的提醒信息数据
        
        注意事项：
        - 产品名称必须与查询结果中的productName字段完全匹配
        - 删除操作不可逆，请确认用户真的要删除该订阅提醒
        - 删除成功后会同时清理fg_remind和fg_remind_info两个表中的相关数据
        - 如果产品名称不存在或不属于当前用户，删除操作将失败
        - 建议在删除前再次确认用户的删除意图，避免误删重要的订阅提醒
        """)
    public ToolCallResponse deleteRemind(
            @ToolMemoryId Object memoryId,
            @P("要删除的订阅提醒的产品名字") String productName) {
        long executionStartTime = System.currentTimeMillis();
        try {
            userContextManager.validateUser(memoryId);
            String userId = userContextManager.getUserId(memoryId);
            if (StrUtil.isBlank(productName)) {
                throw new BaseException("要求用户提供要删除的订阅提醒的产品名字");
            }

            remindService.deleteFgRemindPoByProductName(productName, userId);

            return ToolCallResponse.builder()
                    .toolName("deleteRemind")
                    .toolResult(productName)
                    .toolResultDescription(String.format("成功删除订阅提醒: %s", productName))
                    .toolStatus("success")
                    .executionTime(System.currentTimeMillis() - executionStartTime)
                    .toolVersion("1.0")
                    .build();

        } catch (Exception e) {
            log.error("删除提醒失败", e);
            return ToolCallResponse.builder()
                    .toolName("deleteRemind")
                    .toolResult("删除提醒失败: " + e.getMessage())
                    .toolResultDescription("删除提醒时发生错误")
                    .toolStatus("failed")
                    .executionTime(System.currentTimeMillis() - executionStartTime)
                    .errorMessage(e.getMessage())
                    .toolVersion("1.0")
                    .build();
        }
    }
}

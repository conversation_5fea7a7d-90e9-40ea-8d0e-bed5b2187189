package com.sub.subm.api.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.sub.common.constant.CacheConstants;
import com.sub.common.core.common.BatchRecordPo;
import com.sub.common.core.redis.RedisCache;
import com.sub.common.exception.BaseException;
import com.sub.common.utils.StringUtils;
import com.sub.common.utils.TimeUtils;
import com.sub.subm.api.entity.vo.filterRemindVO;
import com.sub.subm.api.security.SecurityUtils;
import com.sub.subm.api.utils.CurrencyConversionUtil;
import com.sub.subm.dal.dao.product.CurrencyDao;
import com.sub.subm.dal.dao.product.ProductDao;
import com.sub.subm.dal.dao.remind.FgRemindDao;
import com.sub.subm.dal.dao.remind.FgRemindInfoDao;
import com.sub.subm.dal.entity.generate.product.CurrencyPo;
import com.sub.subm.dal.entity.generate.product.ProductPo;
import com.sub.subm.dal.entity.generate.remind.FgRemindInfoPo;
import com.sub.subm.dal.entity.generate.remind.FgRemindPo;
import com.sub.subm.dal.entity.generate.user.UserPo;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class RemindServiceImpl {
    private static final int REMIND_CACHE_TIMEOUT = 15; // 提醒列表缓存15分钟

    @Autowired
    private FgRemindDao fgRemindDao;
    @Autowired
    private ProductDao productDao;
    @Autowired
    private FgRemindInfoDao fgRemindInfoDao;
    @Autowired
    private CurrencyDao currencyDao;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private CurrencyConversionUtil currencyConversionUtil;

    @Autowired
    private UserServiceImpl userService;

    /**
     * 根据条件过滤提醒记录列表
     *
     * @param fgRemindPo 查询条件对象，包含用户ID、状态等过滤条件
     * @return 符合条件的提醒记录列表
     */
    public List<filterRemindVO> filterFgRemindList(FgRemindPo fgRemindPo) {
        // 检查是否适合缓存（只有userId的简单查询）
        if (isCacheableQuery(fgRemindPo)) {
            String cacheKey = CacheConstants.REMIND_LIST_KEY + ":" + fgRemindPo.getUserId();
            
            // 尝试从缓存获取数据
            try {
                @SuppressWarnings("unchecked")
                List<filterRemindVO> cachedData = redisCache.getCacheObject(cacheKey);
                if (cachedData != null) {
                    log.debug("从缓存获取提醒列表数据, userId: {}, key: {}", fgRemindPo.getUserId(), cacheKey);
                    return cachedData;
                }
            } catch (Exception e) {
                log.warn("获取提醒列表缓存失败, userId: {}, error: {}", fgRemindPo.getUserId(), e.getMessage());
            }
        }
        
        // 缓存未命中或不适合缓存，执行原有业务逻辑
        List<FgRemindPo> list = fgRemindDao.filterFgRemindList(fgRemindPo);
        List<filterRemindVO> filterRemindVOList = new ArrayList<>();
        list.forEach(item -> {
            filterRemindVO filterRemindVO = new filterRemindVO();
            BeanUtils.copyProperties(item, filterRemindVO);
            // 设置下次提醒时间
            filterRemindVO.setRemindNextTime(calculateNextRemindTime(item));
            filterRemindVOList.add(filterRemindVO);
        });
        
        // 如果适合缓存，将结果存储到缓存
        if (isCacheableQuery(fgRemindPo)) {
            String cacheKey = CacheConstants.REMIND_LIST_KEY + ":" + fgRemindPo.getUserId();
            try {
                redisCache.setCacheObject(cacheKey, filterRemindVOList, REMIND_CACHE_TIMEOUT, TimeUnit.MINUTES);
                log.debug("提醒列表数据已缓存, userId: {}, key: {}", fgRemindPo.getUserId(), cacheKey);
            } catch (Exception e) {
                log.warn("缓存提醒列表数据失败, userId: {}, error: {}", fgRemindPo.getUserId(), e.getMessage());
            }
        }
        
        return filterRemindVOList;
    }

    /**
     * 根据条件统计提醒记录的金额
     *
     * @param fgRemindPo 查询条件对象
     * @return 统计的金额总数
     */
    public BigDecimal filterFgStatList(FgRemindPo fgRemindPo) {
        return fgRemindDao.filterFgStatList(fgRemindPo);
    }

    /**
     * 计算提醒信息的总金额
     *
     * @param fgRemindInfoPo 提醒信息查询条件
     * @return 总金额，保留两位小数，如果为null则返回0.00
     */
    public BigDecimal totalAmount(FgRemindInfoPo fgRemindInfoPo) {
        BigDecimal totalAmount = fgRemindInfoDao.totalAmount(fgRemindInfoPo);
        if (totalAmount == null) {
            totalAmount = BigDecimal.ZERO;
        }
        return totalAmount.setScale(2, RoundingMode.HALF_UP);
    }


    // ================================================

    /**
     * 计算年统计金额
     * 根据提醒记录的开始时间和提醒周期，计算从开始时间到现在的累计金额
     * @throws BaseException
     */
    public BigDecimal getYearStatAmount() throws BaseException {
        FgRemindPo fgRemindPo = new FgRemindPo();
        fgRemindPo.setUserId(SecurityUtils.getUserId());
        fgRemindPo.setStatus(1); // 只统计有效的提醒记录
        List<FgRemindPo> list = fgRemindDao.filterFgRemindList(fgRemindPo);
        if(list == null || list.size() == 0){
            return BigDecimal.ZERO;
        }

        BigDecimal totalAmount = BigDecimal.ZERO;
        long currentTime = TimeUtils.getCurrentTime();

        for(FgRemindPo remind : list) {
            if(remind.getStartTime() == null || remind.getAmount() == null) {
                continue;
            }
            // 开始时间需要在今年内
            if(remind.getStartTime() < TimeUtils.getYearStartTime()){
                continue;
            }
            // 结束时间需要在今年内
            if(remind.getEndTime() != null && remind.getEndTime() < TimeUtils.getYearEndTime()){
                continue;
            }

            // 如果开始时间大于当前时间，跳过
            if(remind.getStartTime() > currentTime) {
                continue;
            }

            BigDecimal periodAmount = calculatePeriodAmount(remind, currentTime);
            totalAmount = totalAmount.add(periodAmount);
        }

        return totalAmount.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 根据提醒周期计算从开始时间到当前时间的累计金额
     * @param remind 提醒记录
     * @param currentTime 当前时间戳
     * @return 累计金额
     */
    private BigDecimal calculatePeriodAmount(FgRemindPo remind, long currentTime) {
        if(remind.getRemindCycle() == null || remind.getStartTime() == null || remind.getAmount() == null) {
            return BigDecimal.ZERO;
        }

        long startTime = remind.getStartTime();
        long timeDiff = currentTime - startTime;

        // 如果时间差小于0，返回0
        if(timeDiff <= 0) {
            return BigDecimal.ZERO;
        }

        int periodCount = 0;

        switch (remind.getRemindCycle()) {
            case 1: // 年
                periodCount = calculateYearPeriods(startTime, currentTime);
                break;
            case 3: // 月
                periodCount = calculateMonthPeriods(startTime, currentTime);
                break;
            case 4: // 周
                periodCount = calculateWeekPeriods(startTime, currentTime);
                break;
            case 5: // 日
                periodCount = calculateDayPeriods(startTime, currentTime);
                break;
            default:
                return BigDecimal.ZERO;
        }

        return remind.getAmount().multiply(new BigDecimal(periodCount));
    }

    /**
     * 计算年周期数
     */
    private int calculateYearPeriods(long startTime, long currentTime) {
        Calendar startCal = Calendar.getInstance();
        startCal.setTimeInMillis(startTime * 1000);

        Calendar currentCal = Calendar.getInstance();
        currentCal.setTimeInMillis(currentTime * 1000);

        int years = currentCal.get(Calendar.YEAR) - startCal.get(Calendar.YEAR);

        // 如果当前日期还没到开始日期的月日，年数减1
        if (currentCal.get(Calendar.MONTH) < startCal.get(Calendar.MONTH) ||
            (currentCal.get(Calendar.MONTH) == startCal.get(Calendar.MONTH) &&
             currentCal.get(Calendar.DAY_OF_MONTH) < startCal.get(Calendar.DAY_OF_MONTH))) {
            years--;
        }

        return Math.max(0, years + 1); // +1 因为包含开始年份
    }

    /**
     * 计算月周期数
     */
    private int calculateMonthPeriods(long startTime, long currentTime) {
        Calendar startCal = Calendar.getInstance();
        startCal.setTimeInMillis(startTime * 1000);

        Calendar currentCal = Calendar.getInstance();
        currentCal.setTimeInMillis(currentTime * 1000);

        int months = (currentCal.get(Calendar.YEAR) - startCal.get(Calendar.YEAR)) * 12 +
                     currentCal.get(Calendar.MONTH) - startCal.get(Calendar.MONTH);

        // 如果当前日期还没到开始日期的日，月数减1
        if (currentCal.get(Calendar.DAY_OF_MONTH) < startCal.get(Calendar.DAY_OF_MONTH)) {
            months--;
        }

        return Math.max(0, months + 1); // +1 因为包含开始月份
    }

    /**
     * 计算周周期数
     */
    private int calculateWeekPeriods(long startTime, long currentTime) {
        long timeDiffSeconds = currentTime - startTime;
        long days = timeDiffSeconds / (24 * 60 * 60);
        return (int) (days / 7) + 1; // +1 因为包含开始周
    }

    /**
     * 计算日周期数
     */
    private int calculateDayPeriods(long startTime, long currentTime) {
        long timeDiffSeconds = currentTime - startTime;
        long days = timeDiffSeconds / (24 * 60 * 60);
        return (int) days + 1; // +1 因为包含开始日
    }


    // ===================================================

    /**
     * 插入新的提醒记录
     * 使用新事务确保数据一致性，同时插入提醒记录和提醒信息
     *
     * @param fgRemindPo 要插入的提醒记录对象
     * @throws Exception 插入失败时抛出异常并回滚事务
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void insertFgRemindPo(FgRemindPo fgRemindPo) throws BaseException {
        if(fgRemindPo.getAmount() == null || fgRemindPo.getAmount().compareTo(BigDecimal.ZERO) == 0){
            throw new BaseException("提醒金额不能为0");
        }

        UserPo userPo = userService.getByIdUserPo(fgRemindPo.getUserId());
        String userCurrency = userPo.getCurrency();

        BigDecimal rate = currencyConversionUtil.getConversionRate(fgRemindPo.getCurrency(), userCurrency);
        fgRemindPo.setRate(rate);

        if (StringUtils.isNotEmpty(fgRemindPo.getId()) && fgRemindPo.getIsCustom() == 0) {
            ProductPo productPo = productDao.getByProductId(fgRemindPo.getProductId());
            fgRemindPo.setImg(productPo.getIconUrl());
        }

        fgRemindDao.insertFgRemind(fgRemindPo);

        // 生成账单记录（包括历史账单）
        List<FgRemindInfoPo> billRecords = generateBillRecordsForNewSubscription(fgRemindPo);
        if (!billRecords.isEmpty()) {
            fgRemindInfoDao.insertFgRemindInfo(new BatchRecordPo<>(billRecords));
        }



        // 清理缓存
        clearRemindListCache(fgRemindPo.getUserId());
    }

    /**
     * 更新提醒记录
     * 同时更新提醒记录和对应的提醒信息
     *
     * @param fgRemindPo 要更新的提醒记录对象
     */
    public void updateFgRemindPo(FgRemindPo fgRemindPo) {
        // 更新汇率信息
        fgRemindPo = this.getByProductId(fgRemindPo);
        fgRemindDao.updateFgRemind(fgRemindPo);
        FgRemindInfoPo fgRemindInfoPo = new FgRemindInfoPo(fgRemindPo, fgRemindPo.getUpdateTime());
        fgRemindInfoDao.updateFgRemindInfo(fgRemindInfoPo);
        
        // 清理缓存
        clearRemindListCache(fgRemindPo.getUserId());
    }

    /**
     * 批量删除提醒记录
     * 同时删除提醒记录和对应的提醒信息
     *
     * @param arrayId 要删除的记录ID列表
     * @param userId 用户ID，用于权限验证
     */
    public void deleteFgRemindPo(List<String> arrayId, String userId) {
        fgRemindDao.deleteFgRemind(arrayId, userId);
        fgRemindInfoDao.deleteFgRemindInfo(arrayId, userId);
        
        // 清理缓存
        clearRemindListCache(userId);
    }

    /**
     * 根据产品名称删除提醒记录
     *
     * @param productName 产品名称
     * @param userId 用户ID，用于权限验证
     */
    public void deleteFgRemindPoByProductName(String productName, String userId) {
        fgRemindDao.deleteFgRemindByProductName(productName, userId);
        fgRemindInfoDao.deleteFgRemindInfoByProductName(productName, userId);

        // 清理缓存
        clearRemindListCache(userId);
    }

    /**
     * 根据产品ID获取提醒记录详情
     * 为非自定义产品设置图标，并设置汇率信息
     *
     * @param fgRemindPo 包含产品ID和其他查询条件的提醒记录对象
     * @return 完善了图标和汇率信息的提醒记录对象
     */
    public FgRemindPo getByProductId(FgRemindPo fgRemindPo) {
        if (StringUtils.isNotEmpty(fgRemindPo.getId()) && fgRemindPo.getIsCustom() == 0) {
            ProductPo productPo = productDao.getByProductId(fgRemindPo.getProductId());
            fgRemindPo.setImg(productPo.getIconUrl());
        }
        CurrencyPo currencyPo = currencyDao.getCurrencyName(fgRemindPo.getCurrency().trim());
        
        if (currencyPo != null) {
            fgRemindPo.setRate(currencyPo.getExchangeRate());
        }
        return fgRemindPo;
    }

    /**
     * 根据日期获取需要提醒的记录
     * 查询指定用户在指定日期需要提醒的所有有效记录
     *
     * @param data 查询参数，包含用户ID和查询时间戳
     * @return 在指定月份需要提醒的记录列表，如果参数无效则返回空列表
     */
    public List<FgRemindPo> getRemindByDay(String userId, Long time){
        if (userId == null || time == null) {
            return new ArrayList<>();
        }

        // 1. 查询该用户的有效提醒记录
        FgRemindPo queryCondition = new FgRemindPo();
        queryCondition.setUserId(userId);
        queryCondition.setStatus(1); // 有效状态

        List<FgRemindPo> allRemindList = fgRemindDao.filterFgRemindList(queryCondition);

        // 2. 过滤出在查询日期需要提醒的记录
        return allRemindList.stream()
                .filter(remind -> isRemindNeededOnDate(remind, time))
                .collect(Collectors.toList());
    }

    /**
     * 根据月份获取需要提醒的记录列表（新逻辑）
     * 年提醒：判断这个月是不是和开始日期同一个月
     * 周提醒：这个月都要展示
     * 日提醒：这个月都要展示
     * 永久提醒：这个月都要展示
     *
     * @param data 查询参数，包含用户ID和查询时间戳
     * @return 在指定月份需要提醒的记录列表，如果参数无效则返回空列表
     */
    public List<FgRemindPo> getRemindByMonth(String userId, Long time){
        if (userId == null || time == null) {
            return new ArrayList<>();
        }

        // 1. 查询该用户的有效提醒记录
        FgRemindPo queryCondition = new FgRemindPo();
        queryCondition.setUserId(userId);
        queryCondition.setStatus(1); // 有效状态

        List<FgRemindPo> allRemindList = fgRemindDao.filterFgRemindList(queryCondition);

        // 2. 过滤出在查询月份需要提醒的记录
        return allRemindList.stream()
                .filter(remind -> isRemindNeededInMonth(remind, time))
                .collect(Collectors.toList());
    }

    /**
     * 判断提醒记录在指定月份是否需要提醒（新逻辑）
     * 年提醒：判断这个月是不是和开始日期同一个月
     * 周提醒：这个月都要展示
     * 日提醒：这个月都要展示
     * 永久提醒：这个月都要展示
     *
     * @param remind 提醒记录对象，包含开始时间、结束时间、提醒周期等信息
     * @param queryTimestamp 查询时间戳（秒级）
     * @return true表示在该月份需要提醒，false表示不需要提醒
     */
    private boolean isRemindNeededInMonth(FgRemindPo remind, Long queryTimestamp) {
        // 1. 检查基本条件：查询时间戳必须在提醒记录的有效期内
        if (remind.getStartTime() != null && queryTimestamp < remind.getStartTime()) {
            return false; // 还未开始
        }

        if (remind.getEndTime() != null && queryTimestamp >= remind.getEndTime()) {
            return false; // 已过期
        }

        // 2. 根据提醒周期判断该月份是否需要提醒
        Integer remindCycle = remind.getRemindCycle();
        if (remindCycle == null) {
            return false;
        }

        return switch (remindCycle) {
            case 0 -> // 永久 - 这个月都要展示
                    true;
            case 1 -> // 年 - 判断这个月是不是和开始日期同一个月
                    isYearlyRemindNeededInMonth(remind, queryTimestamp);
            case 3 -> // 月 - 每月提醒（按开始日期的每月对应日）
                    true;
            case 4 -> // 周 - 这个月都要展示
                    true;
            case 5 -> // 日 - 这个月都要展示
                    true;
            default -> false;
        };
    }

    /**
     * 判断提醒记录在指定日期是否需要提醒
     * 根据提醒记录的有效期和提醒周期，判断在指定时间戳对应的日期是否需要提醒
     *
     * @param remind 提醒记录对象，包含开始时间、结束时间、提醒周期等信息
     * @param queryTimestamp 查询时间戳（秒级）
     * @return true表示需要提醒，false表示不需要提醒
     */
    private boolean isRemindNeededOnDate(FgRemindPo remind, Long queryTimestamp) {
        // 1. 检查基本条件：查询时间戳必须在提醒记录的有效期内
        if (remind.getStartTime() != null && queryTimestamp < remind.getStartTime()) {
            return false; // 还未开始
        }

        if (remind.getEndTime() != null && queryTimestamp >= remind.getEndTime()) {
            return false; // 已过期
        }

        // 2. 根据提醒周期判断该日期是否需要提醒
        Integer remindCycle = remind.getRemindCycle();
        if (remindCycle == null) {
            return false;
        }

        return switch (remindCycle) {
            case 0 -> // 永久 - 在有效期内每天都提醒
                    true;
            case 1 -> // 年 - 每年提醒一次（按开始日期的周年日）
                    isYearlyRemindNeeded(remind, queryTimestamp);
            case 3 -> // 月 - 每月提醒（按开始日期的每月对应日）
                    isMonthlyRemindNeeded(remind, queryTimestamp);
            case 4 -> // 周 - 每周提醒（每7天提醒一次）
                    isWeeklyRemindNeeded(remind, queryTimestamp);
            case 5 -> // 日 - 每日提醒
                    true;
            default -> false;
        };
    }

    /**
     * 判断年周期提醒是否需要在指定时间提醒
     * 检查查询时间是否与提醒开始时间在同一个月日（周年提醒）
     *
     * @param remind 提醒记录对象，必须包含开始时间
     * @param queryTimestamp 查询时间戳（秒级）
     * @return true表示是周年日期需要提醒，false表示不是周年日期
     */
    private boolean isYearlyRemindNeeded(FgRemindPo remind, Long queryTimestamp) {
        if (remind.getStartTime() == null) {
            return false;
        }

        LocalDateTime startTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(remind.getStartTime()), ZoneId.systemDefault());
        LocalDateTime queryTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(queryTimestamp), ZoneId.systemDefault());

        // 检查是否是开始时间的周年日期（月份和日期都相同）
        return startTime.getMonthValue() == queryTime.getMonthValue()
               && startTime.getDayOfMonth() == queryTime.getDayOfMonth();
    }

    /**
     * 判断年周期提醒在指定月份是否需要提醒
     * 检查查询时间所在月份是否与提醒开始时间在同一个月份
     *
     * @param remind 提醒记录对象，必须包含开始时间
     * @param queryTimestamp 查询时间戳（秒级）
     * @return true表示是同一个月份需要提醒，false表示不是同一个月份
     */
    private boolean isYearlyRemindNeededInMonth(FgRemindPo remind, Long queryTimestamp) {
        if (remind.getStartTime() == null) {
            return false;
        }

        LocalDateTime startTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(remind.getStartTime()), ZoneId.systemDefault());
        LocalDateTime queryTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(queryTimestamp), ZoneId.systemDefault());

        // 检查是否是开始时间的同一个月份
        return startTime.getMonthValue() == queryTime.getMonthValue();
    }

    /**
     * 判断月周期提醒是否需要在指定时间提醒
     * 检查查询时间是否与提醒开始时间在同一个日期（每月对应日）
     *
     * @param remind 提醒记录对象，必须包含开始时间
     * @param queryTimestamp 查询时间戳（秒级）
     * @return true表示是每月对应日期需要提醒，false表示不是对应日期
     */
    private boolean isMonthlyRemindNeeded(FgRemindPo remind, Long queryTimestamp) {
        if (remind.getStartTime() == null) {
            return false;
        }

        LocalDateTime startTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(remind.getStartTime()), ZoneId.systemDefault());
        LocalDateTime queryTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(queryTimestamp), ZoneId.systemDefault());

        // 检查是否是开始时间的每月对应日期
        return startTime.getDayOfMonth() == queryTime.getDayOfMonth();
    }

    /**
     * 判断周周期提醒是否需要在指定时间提醒
     * 计算从开始时间到查询时间的天数差，判断是否是7天周期的提醒日
     *
     * @param remind 提醒记录对象，必须包含开始时间
     * @param queryTimestamp 查询时间戳（秒级）
     * @return true表示该日期需要提醒，false表示该日期不需要提醒
     */
    private boolean isWeeklyRemindNeeded(FgRemindPo remind, Long queryTimestamp) {
        if (remind.getStartTime() == null) {
            return false;
        }

        LocalDateTime startTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(remind.getStartTime()), ZoneId.systemDefault());
        LocalDateTime queryTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(queryTimestamp), ZoneId.systemDefault());

        // 计算从开始时间到查询时间的天数差
        long daysBetween = ChronoUnit.DAYS.between(startTime.toLocalDate(), queryTime.toLocalDate());

        // 如果天数差能被7整除，则表示是7天周期的提醒日
        return daysBetween >= 0 && daysBetween % 7 == 0;
    }

    /**
     * 计算下次提醒时间
     * 根据提醒周期和开始时间计算下次提醒的时间戳
     *
     * @param fgRemindPo 提醒记录对象，包含开始时间和提醒周期
     * @return 下次提醒时间的时间戳，如果无法计算则返回null
     */
    public Long calculateNextRemindTime(FgRemindPo fgRemindPo) {
        if (fgRemindPo == null || fgRemindPo.getStartTime() == null || fgRemindPo.getRemindCycle() == null) {
            return null;
        }

        Long startTime = fgRemindPo.getStartTime();
        Integer remindCycle = fgRemindPo.getRemindCycle();
        Long currentTime = System.currentTimeMillis() / 1000; // 当前时间戳（秒）

        // 如果还未到开始时间，下次提醒时间就是开始时间
        if (currentTime < startTime) {
            return startTime;
        }

        // 如果已过结束时间，则不再提醒
        if (fgRemindPo.getEndTime() != null && currentTime > fgRemindPo.getEndTime()) {
            return null;
        }

        // 将时间戳转换为LocalDateTime进行计算
        LocalDateTime startDateTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(startTime), ZoneId.systemDefault());
        LocalDateTime currentDateTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(currentTime), ZoneId.systemDefault());
        LocalDateTime nextRemindTime;

        switch (remindCycle) {
            case 0: // 永久 - 按月提醒，永远不提醒
                return null;
            case 1: // 年 - 每年提醒一次
                nextRemindTime = calculateNextYearlyRemind(startDateTime, currentDateTime);
                break;
            case 3: // 月 - 每月提醒
                nextRemindTime = calculateNextMonthlyRemind(startDateTime, currentDateTime);
                break;
            case 4: // 周 - 每周提醒
                nextRemindTime = calculateNextWeeklyRemind(startDateTime, currentDateTime);
                break;
            case 5: // 日 - 每日提醒
                nextRemindTime = calculateNextDailyRemind(startDateTime, currentDateTime);
                break;
            default:
                return null;
        }

        // 检查计算出的下次提醒时间是否超过结束时间
        if (fgRemindPo.getEndTime() != null) {
            long nextRemindTimestamp = nextRemindTime.atZone(ZoneId.systemDefault()).toEpochSecond();
            if (nextRemindTimestamp > fgRemindPo.getEndTime()) {
                return null;
            }
        }

        return nextRemindTime.atZone(ZoneId.systemDefault()).toEpochSecond();
    }

    /**
     * 计算下次年度提醒时间
     */
    private LocalDateTime calculateNextYearlyRemind(LocalDateTime startTime, LocalDateTime currentTime) {
        LocalDateTime nextRemind = startTime;

        // 如果当前时间已经过了今年的提醒时间，则计算明年的提醒时间
        while (nextRemind.isBefore(currentTime) || nextRemind.isEqual(currentTime)) {
            nextRemind = nextRemind.plusYears(1);
        }

        return nextRemind;
    }

    /**
     * 计算下次月度提醒时间
     */
    private LocalDateTime calculateNextMonthlyRemind(LocalDateTime startTime, LocalDateTime currentTime) {
        LocalDateTime nextRemind = startTime;

        // 如果当前时间已经过了本月的提醒时间，则计算下个月的提醒时间
        while (nextRemind.isBefore(currentTime) || nextRemind.isEqual(currentTime)) {
            nextRemind = nextRemind.plusMonths(1);
        }

        return nextRemind;
    }

    /**
     * 计算下次周度提醒时间
     */
    private LocalDateTime calculateNextWeeklyRemind(LocalDateTime startTime, LocalDateTime currentTime) {
        LocalDateTime nextRemind = startTime;

        // 如果当前时间已经过了本周的提醒时间，则计算下周的提醒时间
        while (nextRemind.isBefore(currentTime) || nextRemind.isEqual(currentTime)) {
            nextRemind = nextRemind.plusWeeks(1);
        }

        return nextRemind;
    }

    /**
     * 计算下次日度提醒时间
     */
    private LocalDateTime calculateNextDailyRemind(LocalDateTime startTime, LocalDateTime currentTime) {
        LocalDateTime nextRemind = startTime;

        // 如果当前时间已经过了今天的提醒时间，则计算明天的提醒时间
        while (nextRemind.isBefore(currentTime) || nextRemind.isEqual(currentTime)) {
            nextRemind = nextRemind.plusDays(1);
        }

        return nextRemind;
    }

    /**
     * 清理用户的提醒相关缓存
     * 包括提醒列表缓存和提醒统计缓存
     *
     * @param userId 用户ID
     */
    private void clearRemindListCache(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return;
        }

        // 清理提醒列表缓存
        String listCacheKey = CacheConstants.REMIND_LIST_KEY + ":" + userId;
        try {
            boolean deleted = redisCache.deleteObject(listCacheKey);
            if (deleted) {
                log.debug("已清理用户提醒列表缓存, userId: {}, key: {}", userId, listCacheKey);
            }
        } catch (Exception e) {
            log.warn("清理用户提醒列表缓存失败, userId: {}, error: {}", userId, e.getMessage());
        }

        // 清理提醒统计缓存
        String statCacheKey = CacheConstants.REMIND_STAT_KEY + ":" + userId;
        try {
            boolean deleted = redisCache.deleteObject(statCacheKey);
            if (deleted) {
                log.debug("已清理用户提醒统计缓存, userId: {}, key: {}", userId, statCacheKey);
            }
        } catch (Exception e) {
            log.warn("清理用户提醒统计缓存失败, userId: {}, error: {}", userId, e.getMessage());
        }
    }
    
    /**
     * 判断查询是否适合缓存
     * 只对简单查询（仅包含userId）进行缓存
     */
    private boolean isCacheableQuery(FgRemindPo fgRemindPo) {
        if (fgRemindPo == null || StringUtils.isEmpty(fgRemindPo.getUserId())) {
            return false;
        }
        
        // 检查是否只有userId字段有值，其他查询条件字段都为空
        return StringUtils.isEmpty(fgRemindPo.getId()) &&
               StringUtils.isEmpty(fgRemindPo.getProductId()) &&
               StringUtils.isEmpty(fgRemindPo.getProductName()) &&
               StringUtils.isEmpty(fgRemindPo.getCategory()) &&
               fgRemindPo.getStatus() == null &&
               fgRemindPo.getRemindType() == null &&
               fgRemindPo.getCreateTime() == null &&
               fgRemindPo.getStartTime() == null &&
               fgRemindPo.getEndTime() == null;
    }

    /**
     * 为新订阅生成账单记录（包括历史账单）
     *
     * @param fgRemindPo 新添加的订阅
     * @return 账单记录列表
     */
    private List<FgRemindInfoPo> generateBillRecordsForNewSubscription(FgRemindPo fgRemindPo) {
        List<FgRemindInfoPo> billRecords = new ArrayList<>();

        if (fgRemindPo.getStartTime() == null || fgRemindPo.getRemindCycle() == null) {
            return billRecords;
        }

        long currentTime = TimeUtils.getCurrentTime();
        long startTime = fgRemindPo.getStartTime();

        // 永久订阅只生成一次账单记录
        if (fgRemindPo.getRemindCycle() == 0) {
            FgRemindInfoPo billRecord = new FgRemindInfoPo(fgRemindPo, startTime);
            billRecords.add(billRecord);
            return billRecords;
        }

        // 如果开始时间就是当前时间或未来时间，只生成一条当前账单记录
        if (startTime >= currentTime) {
            FgRemindInfoPo billRecord = new FgRemindInfoPo(fgRemindPo, startTime);
            billRecords.add(billRecord);
            return billRecords;
        }

        // 历史订阅：生成从开始时间到当前时间的所有账单记录
        Calendar billCal = Calendar.getInstance();
        billCal.setTimeInMillis(startTime * 1000);

        while (billCal.getTimeInMillis() / 1000 <= currentTime) {
            long billTime = billCal.getTimeInMillis() / 1000;
            FgRemindInfoPo billRecord = new FgRemindInfoPo(fgRemindPo, billTime);
            billRecords.add(billRecord);

            // 移动到下一个周期
            switch (fgRemindPo.getRemindCycle()) {
                case 1: // 年
                    billCal.add(Calendar.YEAR, 1);
                    break;
                case 3: // 月
                    billCal.add(Calendar.MONTH, 1);
                    break;
                case 4: // 周
                    billCal.add(Calendar.WEEK_OF_YEAR, 1);
                    break;
                case 5: // 日
                    billCal.add(Calendar.DAY_OF_MONTH, 1);
                    break;
                default:
                    // 未知周期，只生成开始时间的账单
                    if (billRecords.isEmpty()) {
                        FgRemindInfoPo defaultBillRecord = new FgRemindInfoPo(fgRemindPo, startTime);
                        billRecords.add(defaultBillRecord);
                    }
                    
                    break;
            }

            // 防止无限循环，最多生成100条历史记录
            if (billRecords.size() >= 100) {
                break;
            }
        }

        return billRecords;
    }
}

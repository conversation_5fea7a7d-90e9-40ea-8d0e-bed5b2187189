package com.sub.subm.api.service.assistant.tools;

import java.util.List;

import org.springframework.stereotype.Component;

import com.sub.subm.api.service.ProjectServiceImpl;
import com.sub.subm.api.service.assistant.model.dto.ToolCallResponse;
import com.sub.subm.dal.entity.generate.product.ProductPo;

import dev.langchain4j.agent.tool.Tool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 产品工具类
 */
@RequiredArgsConstructor
@Component
@Slf4j
public class ProductTool {

    private final ProjectServiceImpl productService;

    @Tool("获取所有订阅产品的详细信息")
    public ToolCallResponse getAllProduct() {
        long startTime = System.currentTimeMillis();
        List<ProductPo> productList = productService.filterProductList();
        
        return ToolCallResponse.builder()
                .toolName("getAllProduct")
                .toolResult(productList)
                .toolResultDescription("获取所有产品信息")
                .toolStatus("success")
                .executionTime(System.currentTimeMillis() - startTime)
                .toolVersion("1.0")
                .build();
    }
}

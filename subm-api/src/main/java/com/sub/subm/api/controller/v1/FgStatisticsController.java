package com.sub.subm.api.controller.v1;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.sub.common.core.common.CommonResult;
import com.sub.common.exception.BaseException;
import com.sub.subm.api.security.SecurityUtils;
import com.sub.subm.api.service.RemindInfoServiceImpl;
import com.sub.subm.dal.entity.generate.remind.FgRemindInfoPo;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@Tag(name = "统计页面 api")
@RequestMapping(value = "/api/v1/statics")
public class FgStatisticsController extends CommonResult {
    

    @Autowired
    private RemindInfoServiceImpl remindInfoService;

    @Operation(summary = "统计页面列表")
    @RequestMapping(value = "listStatistics", method = RequestMethod.GET)
    public CommonResult listStatistics(FgRemindInfoPo remindInfoPo) throws BaseException {
        String userId = SecurityUtils.getUserId();
        remindInfoPo.setUserId(userId);
        Map<String, Object> statics = new HashMap<>();
        statics.put("total", remindInfoService.totalAmount(remindInfoPo));

        Map<String, Object> date = new HashMap<>();
        date.put("groupBy", remindInfoPo.getDate());
        remindInfoPo.setParams(date);
        statics.put("date", remindInfoService.filterFgRemindInfoList(remindInfoPo));

        Map<String, Object> map = new HashMap<>();
        map.put("groupBy", "category");
        remindInfoPo.setParams(map);
        statics.put("category", remindInfoService.filterFgRemindInfoList(remindInfoPo));
        return ok(statics);
    }
}

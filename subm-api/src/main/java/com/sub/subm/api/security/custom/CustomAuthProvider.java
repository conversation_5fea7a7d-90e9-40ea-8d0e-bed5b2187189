package com.sub.subm.api.security.custom;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import com.sub.subm.api.entity.vo.LoginUser;


/**
 * 自定义安全 提供者
 */
public class CustomAuthProvider implements AuthenticationProvider {
    private Logger log = LoggerFactory.getLogger(CustomAuthProvider.class);

    @Autowired
    private UserDetailsService userDetailsService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        CustomAuthenticationToken customAuthenticationToken = (CustomAuthenticationToken) authentication;
        LoginUser loginUser = (LoginUser) userDetailsService.loadUserByUsername(customAuthenticationToken.getAccount());
        if (loginUser == null) {
            throw new UsernameNotFoundException("user.not.exists");
        }
        return new CustomAuthenticationToken(loginUser, null, loginUser.getAuthorities());
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return CustomAuthenticationToken.class.isAssignableFrom(authentication);
    }

    public void setUserDetailsService(UserDetailsService userDetailsService) {
        this.userDetailsService = userDetailsService;
    }
}

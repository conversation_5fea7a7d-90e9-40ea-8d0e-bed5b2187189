package com.sub.subm.api.manager;

import java.util.TimerTask;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sub.common.utils.ServletUtils;
import com.sub.common.utils.TimeUtils;
import com.sub.common.utils.ip.NetworkUtil;
import com.sub.common.utils.spring.SpringUtils;
import com.sub.subm.api.service.DataRecordServiceImpl;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 异步工厂（产生任务用）
 */
public class AsyncFactory {
    private static final Logger log = LoggerFactory.getLogger(AsyncFactory.class);

    /**
     * 记录请求数据
     */
    public static TimerTask recordRequestData(HttpServletRequest request) {
        String ipAddress = NetworkUtil.getIpAddr();
        return new TimerTask() {
            @Override
            public void run() {
                SpringUtils.getBean(DataRecordServiceImpl.class).recordRequestLog(request, ipAddress);
            }
        };
    }

    /**
     * 登录成功后, 数据处理
     */
    public static TimerTask loginSuccessHandler(String userId, String account, Integer channel) {
        HttpServletRequest request = ServletUtils.getRequest();
        String ipAddress = NetworkUtil.getIpAddr();
        Long currentTime = TimeUtils.getCurrentTime();
        return new TimerTask() {
            @Override
            public void run() {
                SpringUtils.getBean(DataRecordServiceImpl.class).recordLoginLog(
                        request, userId, ipAddress, currentTime, account, channel
                );
            }
        };
    }
}

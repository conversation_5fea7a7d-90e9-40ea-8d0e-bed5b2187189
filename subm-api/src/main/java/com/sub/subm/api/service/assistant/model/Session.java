package com.sub.subm.api.service.assistant.model;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 会话模型
 */
@Data
public class Session implements Serializable {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户默认货币类型
     */
    private String currency;

    /**
     * 会话创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后活跃时间
     */
    private LocalDateTime lastActiveTime;

    /**
     * 会话状态 (ACTIVE, INACTIVE, EXPIRED)
     */
    private String status;

    /**
     * 会话配置
     */
    private SessionConfig config;

    public Session() {
        this.sessionId = UUID.randomUUID().toString();
        this.createTime = LocalDateTime.now();
        this.lastActiveTime = LocalDateTime.now();
        this.status = "ACTIVE";
        this.config = new SessionConfig();
    }

    public Session(String userId, String userName) {
        this();
        this.userId = userId;
        this.userName = userName;
    }

    /**
     * 更新最后活跃时间
     */
    public void updateLastActiveTime() {
        this.lastActiveTime = LocalDateTime.now();
    }

    /**
     * 检查会话是否过期
     */
    public boolean isExpired() {
        if (config == null || config.getSessionTimeoutMinutes() == null) {
            return false;
        }

        LocalDateTime expireTime = lastActiveTime.plusMinutes(config.getSessionTimeoutMinutes());
        return LocalDateTime.now().isAfter(expireTime);
    }
}

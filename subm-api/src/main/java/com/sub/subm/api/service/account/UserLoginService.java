package com.sub.subm.api.service.account;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import com.sub.common.constant.UserConstants;
import com.sub.common.exception.BaseException;
import com.sub.common.utils.MessageUtils;
import com.sub.common.utils.StringUtils;
import com.sub.common.utils.TimeUtils;
import com.sub.common.utils.auth.AuthUtils;
import com.sub.common.utils.ip.NetworkUtil;
import com.sub.framework.security.context.AuthenticationContextHolder;
import com.sub.subm.api.entity.dto.UserLoginDTOs;
import com.sub.subm.api.entity.vo.LoginUser;
import com.sub.subm.api.security.TokenUtil;
import com.sub.subm.api.security.custom.CustomAuthenticationToken;
import com.sub.subm.dal.dao.user.UserAuthDao;
import com.sub.subm.dal.dao.user.UserDao;
import com.sub.subm.dal.entity.generate.user.UserAuthPo;
import com.sub.subm.dal.entity.generate.user.UserPo;

import jakarta.annotation.Resource;

@Service
public class UserLoginService {
    private static Logger log = LoggerFactory.getLogger(UserLoginService.class);

    @Autowired
    private UserDao userDao;
    @Autowired
    private UserAuthDao userAuthDao;
    @Autowired
    private TokenUtil tokenUtil;
    @Resource
    private AuthenticationManager authenticationManager;

    /**
     * 根据 账号 和 渠道 获取 用户信息
     *
     * @param account
     * @param channel
     * @return
     */
    public UserPo getUserDetail(String account, Integer channel) {
        return userDao.userLogin(account, channel);
    }


    /**
     * 用户登录
     * @param account
     * @param channel
     * @param pwd
     */
    public LoginUser userLogin(String account, Integer channel, Integer type, String pwd) throws BaseException {
        Authentication authentication = null;
        try {
            CustomAuthenticationToken authenticationToken = new CustomAuthenticationToken(account, pwd, channel, type);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            if (e instanceof BaseException) {
                BaseException baseException = (BaseException) e;
                String hitMsg = MessageUtils.message(baseException.getMsg());
                // AsyncManager.me().execute(AsyncFactory.recordLogin(account, Constants.LOGIN_FAIL, hitMsg, channel));
                throw new BaseException(baseException.getCode(), hitMsg);
            } else {
                String hitMsg = MessageUtils.message("err.server");
                // AsyncManager.me().execute(AsyncFactory.recordLogin(account, Constants.LOGIN_FAIL, hitMsg , channel));
                throw new BaseException(hitMsg);
            }
        } finally {
            AuthenticationContextHolder.clearContext();
        }
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        return loginUser;
    }

    /**
     * 三方登录与注册
     * @param data
     */
    public void authRegister(UserLoginDTOs.AuthRegisterDTO data) throws BaseException {
        UserPo userPo = userDao.userLogin(data.getUuid(), data.getChannel());
        log.info(data.getAccount());
        if (userPo == null && StringUtils.isNotEmpty(data.getAccount())) {
            userPo = userDao.userLogin(data.getAccount(), 0);
        }
        UserAuthPo authPo = userAuthDao.getUserAuthPo(data.getUuid(), data.getChannel());
        if (userPo == null) {
            userPo = UserPo.registerUser(data.getName(), data.getAvatar(),
                    data.getUuid(), data.getAccount(), data.getChannel());
            userDao.insertUser(userPo);
        }
        if (authPo == null) {
            insertUserAuth(data, userPo.getId(), TimeUtils.getCurrentTime());
        } else {
            UserPo dbUser = UserPo.resultChannelAccount(new UserPo(), data.getUuid(),
                    data.getAccount(), data.getChannel());
            dbUser.setId(userPo.getId());
            dbUser.setUpdateTime(TimeUtils.getCurrentTime());
            userDao.updateUser(dbUser);
        }
    }

    /**
     *  创建时间
     */
    public void insertUserAuth(UserLoginDTOs.AuthRegisterDTO data, String userId, Long currentTime) {
        UserAuthPo newAuth = new UserAuthPo();
        BeanUtils.copyProperties(data, newAuth);
        newAuth.setUserId(userId);
        newAuth.setCreateTime(currentTime);
        newAuth.setStatus(1);
        newAuth.setChannel(data.getChannel().toString());
        userAuthDao.insertUserAuthPo(newAuth);
    }

    /**
     * 用户注册
     */
    public void registerUserPo(UserPo userPo) {
        userDao.insertUser(userPo);
    }

    /**
     * 普通用户注册时构建最基础数据
     */
    public void builderSysUserPo(UserLoginDTOs.RegisterTDO data) {
        UserPo user = new UserPo();
        user.setRoleId(UserConstants.REGISTER_USER_ROLE);                         // 最基础的用户权限
        user.setSalt(StringUtils.verifySalt(6));                             // 密码盐
        user.setName(data.getName());
        user.setPassword(AuthUtils.md5Hex(data.getPwd(), user.getSalt()));
        user.setMotto(data.getMotto());
        user.setCreateTime(TimeUtils.getCurrentTime());
        user.setEnable(1);
        user.setLastOnlineTime(user.getCreateTime());
        user.setIpAddress(NetworkUtil.getIpAddr());
        user.setCreateIp(user.getIpAddress());

        user = UserPo.resultChannelAccount(user, null, data.getAccount(), data.getChannel());
        registerUserPo(user);        // 将用户等数据写入数据库
    }

    /**
     * 修改用户登录密码
     */
    public void updateUserLoginPwd(String id, String pwd) {
        UserPo userPo = new UserPo();
        userPo.setId(id);
        userPo.setSalt(StringUtils.verifySalt(6)); // 密码盐
        userPo.setPassword(AuthUtils.md5Hex(pwd, userPo.getSalt()));
        userDao.updateUser(userPo);
    }

    /**
     * 根据用户id 获取用户基础信息
     * @param userId
     */
    public UserPo getUserBasicInfo(String userId) {
        return userDao.getByIdUser(userId);
    }


}

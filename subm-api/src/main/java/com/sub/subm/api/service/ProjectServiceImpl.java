package com.sub.subm.api.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sub.subm.dal.dao.product.CurrencyDao;
import com.sub.subm.dal.dao.product.ProductCategoryDao;
import com.sub.subm.dal.dao.product.ProductDao;
import com.sub.subm.dal.dao.product.ProductPlanDao;
import com.sub.subm.dal.dao.product.RegionDao;
import com.sub.subm.dal.entity.generate.product.ProductCategoryPo;
import com.sub.subm.dal.entity.generate.product.ProductPlanPo;
import com.sub.subm.dal.entity.generate.product.ProductPo;
import com.sub.subm.dal.entity.generate.product.RegionPo;

@Service
public class ProjectServiceImpl {
    @Autowired
    private ProductPlanDao productPlanDao;
    @Autowired
    private ProductCategoryDao productCategoryDao;
    @Autowired
    private ProductDao productDao;
    @Autowired
    private RegionDao regionDao;
    @Autowired
    private CurrencyDao currencyDao;

    /**
     * 获取所有地区
     * @return
     */
    public List<RegionPo> filterRegionList() {
        return regionDao.getAllProductRegionList();
    }

    /**
     * 获取所有计划
     */
    public List<ProductPlanPo> filterProductPlanList(ProductPlanPo productPlanPo) {
        return productPlanDao.filterProductPlanList(productPlanPo);
    }

    /**
     * 获取所有种类
     */
    public List<ProductCategoryPo> getAllProductCategoryList() {
        return productCategoryDao.getAllProductCategoryList();
    }

    /**
     * 获取所有产品
     */
    public List<ProductPo> filterProductList() {
        return productDao.filterProductList();
    }

    /**
     * 获取所有产品计划币种
     */
    public List<String> getProductPlanCurrency() {
        return productPlanDao.getProductPlanCurrency();
    }
}

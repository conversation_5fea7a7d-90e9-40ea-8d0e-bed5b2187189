package com.sub.subm.api.task.scheduled;

import java.math.BigDecimal;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.sub.common.utils.TimeUtils;
import com.sub.subm.dal.dao.product.CurrencyDao;
import com.sub.subm.dal.entity.generate.product.CurrencyPo;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;

/**
 * 外汇汇率定时任务
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Component
public class ExchangeRateScheduledTask {

    private static final Logger log = LoggerFactory.getLogger(ExchangeRateScheduledTask.class);

    /**
     * FCS API 外汇汇率接口基础地址
     */
    private static final String FCS_API_BASE_URL = "https://fcsapi.com/api-v3/forex/latest";
    private static final String FCS_API_ACCESS_KEY = "kcFeG7qnGxN30Yx1DzBjVZSm";

    @Autowired
    private CurrencyDao currencyDao;

    /**
     * 构建动态的FCS API URL
     * @return 包含所有货币对的API URL
     */
    private String buildFcsApiUrl() {
        try {
            // 从数据库获取所有货币列表
            List<CurrencyPo> currencies = currencyDao.filterCurrencyList();

            if (currencies == null || currencies.isEmpty()) {
                log.warn("数据库中没有找到货币数据，使用默认symbol");
                return FCS_API_BASE_URL + "?symbol=all_forex&access_key=" + FCS_API_ACCESS_KEY;
            }

            // 构建货币对字符串，格式为 USD/CNY,USD/JPY
            StringBuilder symbolBuilder = new StringBuilder();
            for (CurrencyPo currency : currencies) {
                if (currency.getSimple() != null && !currency.getSimple().equals("USD")) {
                    if (symbolBuilder.length() > 0) {
                        symbolBuilder.append(",");
                    }
                    symbolBuilder.append("USD/").append(currency.getSimple());
                }
            }

            String symbols = symbolBuilder.toString();
            if (symbols.isEmpty()) {
                log.warn("没有找到有效的货币代码，使用默认symbol");
                return FCS_API_BASE_URL + "?symbol=all_forex&access_key=" + FCS_API_ACCESS_KEY;
            }

            log.info("构建的货币对参数: {}", symbols);
            return FCS_API_BASE_URL + "?symbol=" + symbols + "&access_key=" + FCS_API_ACCESS_KEY;

        } catch (Exception e) {
            log.error("构建FCS API URL失败，使用默认URL", e);
            return FCS_API_BASE_URL + "?symbol=all_forex&access_key=" + FCS_API_ACCESS_KEY;
        }
    }

    /**
     * 定时获取外汇汇率
     * 每天凌晨0点执行一次
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void fetchExchangeRates() {
        log.info("开始执行外汇汇率获取任务");
        try {
            // 构建动态API URL
            String apiUrl = buildFcsApiUrl();

            // 使用Hutool发送HTTP请求获取汇率数据
            HttpResponse response = HttpRequest.get(apiUrl)
                .timeout(30000)
                .execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("成功获取外汇汇率数据，响应长度: {}", responseBody.length());

                // 解析JSON响应
                JSONObject jsonResponse = JSONObject.parseObject(responseBody);
                if (jsonResponse.getBoolean("status") && jsonResponse.containsKey("response")) {
                    JSONArray ratesArray = jsonResponse.getJSONArray("response");

                    // 处理汇率数据
                    processExchangeRates(ratesArray);
                    log.info("外汇汇率获取任务执行完成，共处理 {} 条汇率数据", ratesArray.size());
                } else {
                    log.warn("外汇汇率API返回状态异常: {}", jsonResponse.getString("msg"));
                }
            } else {
                log.error("获取外汇汇率失败，HTTP状态码: {}, 错误信息: {}",
                    response.getStatus(), response.body());
            }
        } catch (Exception e) {
            log.error("外汇汇率获取任务执行失败", e);
        }
    }

    /**
     * 处理汇率数据并更新数据库
     *
     * @param ratesArray 汇率数据数组
     */
    private void processExchangeRates(JSONArray ratesArray) {
        if (ratesArray == null || ratesArray.isEmpty()) {
            log.warn("汇率数据为空，跳过处理");
            return;
        }

        // 获取数据库中现有的货币列表
        List<CurrencyPo> existingCurrencies = currencyDao.filterCurrencyList();

        int updatedCount = 0;
        for (int i = 0; i < ratesArray.size(); i++) {
            try {
                JSONObject rateData = ratesArray.getJSONObject(i);
                String symbol = rateData.getString("s"); // 货币对符号，如 "USD/CNY"
                BigDecimal price = rateData.getBigDecimal("c"); // 当前价格

                // 解析货币对，格式为 "USD/CNY"，使用斜杠分隔
                if (symbol != null && symbol.contains("/")) {
                    String[] currencyPair = symbol.split("/");
                    if (currencyPair.length == 2) {
                        String baseCurrency = currencyPair[0]; // 基础货币
                        String quoteCurrency = currencyPair[1]; // 报价货币

                        // 验证基础货币是USD（应该都是USD，因为我们构建的URL就是USD/XXX格式）
                        if ("USD".equals(baseCurrency)) {
                            // 直接更新报价货币对USD的汇率
                            updateCurrencyRate(existingCurrencies, quoteCurrency, price);
                            updatedCount++;
                            log.debug("更新汇率: {} = {}", symbol, price);
                        } else {
                            log.warn("意外的货币对格式: {}，期望基础货币为USD", symbol);
                        }
                    } else {
                        log.warn("无法解析货币对格式: {}", symbol);
                    }
                } else {
                    log.warn("货币对格式不包含斜杠: {}", symbol);
                }
            } catch (Exception e) {
                log.warn("处理单条汇率数据失败: {}", e.getMessage());
            }
        }

        log.info("成功更新 {} 条货币汇率", updatedCount);
    }

    /**
     * 更新指定货币的汇率
     *
     * @param existingCurrencies 现有货币列表
     * @param currencyCode 货币代码
     * @param exchangeRate 汇率
     */
    private void updateCurrencyRate(List<CurrencyPo> existingCurrencies, String currencyCode, BigDecimal exchangeRate) {
        // 查找是否存在该货币
        CurrencyPo targetCurrency = existingCurrencies.stream()
            .filter(currency -> currencyCode.equals(currency.getSimple()))
            .findFirst()
            .orElse(null);

        if (targetCurrency != null) {
            // 更新汇率
            targetCurrency.setExchangeRate(exchangeRate);
            targetCurrency.setUpdateTime(TimeUtils.getCurrentTime());

            int result = currencyDao.updateCurrencyList(targetCurrency);
            if (result > 0) {
                log.debug("成功更新货币 {} 的汇率为: {}", currencyCode, exchangeRate);
            } else {
                log.warn("更新货币 {} 的汇率失败", currencyCode);
            }
        } else {
            log.debug("数据库中未找到货币: {}", currencyCode);
        }
    }
}

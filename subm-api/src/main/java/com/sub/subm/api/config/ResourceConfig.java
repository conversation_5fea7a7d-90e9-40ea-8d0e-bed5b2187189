package com.sub.subm.api.config;

import java.io.File;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;

import com.sub.framework.config.ResourceBasic;

import jakarta.annotation.PostConstruct;

@Configuration
public class ResourceConfig extends ResourceBasic {

    @PostConstruct
    public void initUploadDirectories() {
        // 确保uploads目录及其子目录存在
        String projectRoot = System.getProperty("user.dir");
        String uploadsPath = projectRoot + "/uploads/";

        String[] directories = {"avatar", "feeds", "opml", "i18", "img"};
        for (String dir : directories) {
            File directory = new File(uploadsPath + dir);
            if (!directory.exists()) {
                directory.mkdirs();
            }
        }
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 使用项目根目录下的uploads文件夹
        String projectRoot = System.getProperty("user.dir");
        String uploadsPath = projectRoot + "/uploads/";

        final String[] resources = {
                "file:" + uploadsPath + "avatar/",
                "file:" + uploadsPath + "feeds/",
                "file:" + uploadsPath + "opml/",
                "file:" + uploadsPath + "i18/",
                "file:" + uploadsPath + "img/"
        };
        final String[] handlers = {
                "/avatar/**",
                "/feeds/**",
                "/opml/**",
                "/i18/**",
                "/img/**"
        };
        registry.addResourceHandler(handlers)
                .addResourceLocations(resources);
    }
}

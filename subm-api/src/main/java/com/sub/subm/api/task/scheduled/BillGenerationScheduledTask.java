package com.sub.subm.api.task.scheduled;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.sub.common.core.common.BatchRecordPo;
import com.sub.common.utils.TimeUtils;
import com.sub.subm.dal.dao.remind.FgRemindDao;
import com.sub.subm.dal.dao.remind.FgRemindInfoDao;
import com.sub.subm.dal.entity.generate.remind.FgRemindInfoPo;
import com.sub.subm.dal.entity.generate.remind.FgRemindPo;

/**
 * 账单生成定时任务
 * 每天凌晨1点执行，扫描所有订阅，为到期的账单周期生成账单记录
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Component
public class BillGenerationScheduledTask {

    private static final Logger log = LoggerFactory.getLogger(BillGenerationScheduledTask.class);

    @Autowired
    private FgRemindDao fgRemindDao;

    @Autowired
    private FgRemindInfoDao fgRemindInfoDao;

    /**
     * 定时生成账单记录
     * 每天凌晨1点执行一次
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void generateBillRecords() {
        log.info("开始执行账单生成任务");
        try {
            // 获取当前时间
            long currentTime = TimeUtils.getCurrentTime();
            
            // 查询所有活跃的订阅
            FgRemindPo queryParam = new FgRemindPo();
            queryParam.setStatus(1); // 只查询状态为1（活跃）的订阅
            List<FgRemindPo> activeSubscriptions = fgRemindDao.filterFgRemindList(queryParam);
            
            if (activeSubscriptions == null || activeSubscriptions.isEmpty()) {
                log.info("没有找到活跃的订阅，跳过账单生成");
                return;
            }
            
            log.info("找到 {} 个活跃订阅，开始检查账单生成需求", activeSubscriptions.size());
            
            List<FgRemindInfoPo> billsToGenerate = new ArrayList<>();
            
            // 遍历每个订阅，检查是否需要生成账单
            for (FgRemindPo subscription : activeSubscriptions) {
                try {
                    log.debug("检查订阅: {} ({}), 周期: {}, 开始时间: {}",
                            subscription.getProductName(), subscription.getId(),
                            subscription.getRemindCycle(), subscription.getStartTime());

                    if (shouldGenerateBill(subscription, currentTime)) {
                        // 计算账单周期开始时间
                        long billPeriodStartTime = calculateBillPeriodStartTime(subscription, currentTime);

                        // 创建账单记录
                        FgRemindInfoPo billRecord = new FgRemindInfoPo(subscription, billPeriodStartTime);
                        billsToGenerate.add(billRecord);

                        log.info("为订阅 {} ({}) 生成账单记录，周期: {}, 周期开始时间: {}",
                                subscription.getProductName(), subscription.getId(),
                                getCycleDescription(subscription.getRemindCycle()), billPeriodStartTime);
                    } else {
                        log.debug("订阅 {} ({}) 暂不需要生成账单",
                                subscription.getProductName(), subscription.getId());
                    }
                } catch (Exception e) {
                    log.error("处理订阅 {} ({}) 时发生错误",
                            subscription.getProductName(), subscription.getId(), e);
                }
            }
            
            // 批量插入账单记录
            if (!billsToGenerate.isEmpty()) {
                BatchRecordPo<FgRemindInfoPo> batchRecord = new BatchRecordPo<>(billsToGenerate);
                int insertCount = fgRemindInfoDao.insertFgRemindInfo(batchRecord);
                log.info("账单生成任务完成，成功生成 {} 条账单记录", insertCount);
            } else {
                log.info("账单生成任务完成，没有需要生成的账单记录");
            }
            
        } catch (Exception e) {
            log.error("账单生成任务执行失败", e);
        }
    }

    /**
     * 判断是否需要为订阅生成账单记录
     *
     * @param subscription 订阅信息
     * @param currentTime 当前时间
     * @return 是否需要生成账单
     */
    private boolean shouldGenerateBill(FgRemindPo subscription, long currentTime) {
        // 如果没有设置提醒周期，不生成账单
        if (subscription.getRemindCycle() == null) {
            return false;
        }

        // 如果订阅还没开始，不生成账单
        if (subscription.getStartTime() == null || subscription.getStartTime() > currentTime) {
            return false;
        }

        // 如果订阅已结束，不生成账单
        if (subscription.getEndTime() != null && subscription.getEndTime() < currentTime) {
            return false;
        }

        // 永久订阅（remindCycle = 0）只在开始时生成一次账单
        if (subscription.getRemindCycle() == 0) {
            return !hasBillRecordForPeriod(subscription, subscription.getStartTime());
        }

        // 计算下一个账单周期的开始时间
        long nextBillTime = calculateNextBillTime(subscription, currentTime);

        // 如果下一个账单时间是今天或之前，需要生成账单
        long todayStart = getTodayStartTime();
        long tomorrowStart = todayStart + 24 * 60 * 60; // 明天开始时间

        // 检查是否已经存在该周期的账单记录，避免重复生成
        if (nextBillTime >= todayStart && nextBillTime < tomorrowStart) {
            return !hasBillRecordForPeriod(subscription, nextBillTime);
        }

        return false;
    }

    /**
     * 检查指定周期是否已存在账单记录
     *
     * @param subscription 订阅信息
     * @param periodStartTime 周期开始时间
     * @return 是否已存在账单记录
     */
    private boolean hasBillRecordForPeriod(FgRemindPo subscription, long periodStartTime) {
        try {
            FgRemindInfoPo queryParam = new FgRemindInfoPo();
            queryParam.setRemindId(subscription.getId());
            queryParam.setUserId(subscription.getUserId());

            // 查询该订阅的所有账单记录
            List<FgRemindInfoPo> existingBills = fgRemindInfoDao.filterFgRemindInfoList(queryParam);

            if (existingBills == null || existingBills.isEmpty()) {
                return false;
            }

            // 根据提醒周期判断是否存在相同周期的账单记录
            int cycle = subscription.getRemindCycle();

            for (FgRemindInfoPo bill : existingBills) {
                if (bill.getCreateTime() != null) {
                    if (isInSameBillingPeriod(bill.getCreateTime(), periodStartTime, cycle)) {
                        log.debug("订阅 {} 在周期 {} 已存在账单记录，跳过生成",
                                subscription.getId(), periodStartTime);
                        return true;
                    }
                }
            }

            return false;
        } catch (Exception e) {
            log.warn("检查账单记录时发生错误，订阅ID: {}", subscription.getId(), e);
            return true; // 出错时保守处理，避免重复生成
        }
    }

    /**
     * 判断两个时间是否在同一个账单周期内
     *
     * @param existingBillTime 已存在账单的时间
     * @param newPeriodStartTime 新周期开始时间
     * @param cycle 提醒周期（0永久 1年 3月 4周 5日）
     * @return 是否在同一周期
     */
    private boolean isInSameBillingPeriod(long existingBillTime, long newPeriodStartTime, int cycle) {
        // 永久订阅只生成一次账单
        if (cycle == 0) {
            return true; // 永久订阅已存在账单就不再生成
        }

        Calendar existingCal = Calendar.getInstance();
        existingCal.setTimeInMillis(existingBillTime * 1000);

        Calendar newCal = Calendar.getInstance();
        newCal.setTimeInMillis(newPeriodStartTime * 1000);

        switch (cycle) {
            case 1: // 年
                return existingCal.get(Calendar.YEAR) == newCal.get(Calendar.YEAR) &&
                       existingCal.get(Calendar.MONTH) == newCal.get(Calendar.MONTH) &&
                       existingCal.get(Calendar.DAY_OF_MONTH) == newCal.get(Calendar.DAY_OF_MONTH);

            case 3: // 月
                return existingCal.get(Calendar.YEAR) == newCal.get(Calendar.YEAR) &&
                       existingCal.get(Calendar.MONTH) == newCal.get(Calendar.MONTH) &&
                       existingCal.get(Calendar.DAY_OF_MONTH) == newCal.get(Calendar.DAY_OF_MONTH);

            case 4: // 周
                // 计算周期开始时间，判断是否在同一周期
                long weekInSeconds = 7 * 24 * 60 * 60;
                long timeDiff = Math.abs(existingBillTime - newPeriodStartTime);
                return timeDiff < weekInSeconds;

            case 5: // 日
                // 判断是否是同一天
                return existingCal.get(Calendar.YEAR) == newCal.get(Calendar.YEAR) &&
                       existingCal.get(Calendar.DAY_OF_YEAR) == newCal.get(Calendar.DAY_OF_YEAR);

            default:
                return false;
        }
    }

    /**
     * 计算下一个账单周期的开始时间
     *
     * @param subscription 订阅信息
     * @param currentTime 当前时间
     * @return 下一个账单时间
     */
    private long calculateNextBillTime(FgRemindPo subscription, long currentTime) {
        long startTime = subscription.getStartTime();
        int cycle = subscription.getRemindCycle();

        // 永久订阅只在开始时生成一次账单
        if (cycle == 0) {
            return startTime;
        }

        Calendar startCal = Calendar.getInstance();
        startCal.setTimeInMillis(startTime * 1000);

        Calendar nextBillCal = Calendar.getInstance();
        nextBillCal.setTimeInMillis(startTime * 1000);

        switch (cycle) {
            case 1: // 年
                while (nextBillCal.getTimeInMillis() / 1000 <= currentTime) {
                    nextBillCal.add(Calendar.YEAR, 1);
                }
                break;
            case 3: // 月
                while (nextBillCal.getTimeInMillis() / 1000 <= currentTime) {
                    nextBillCal.add(Calendar.MONTH, 1);
                }
                break;
            case 4: // 周
                while (nextBillCal.getTimeInMillis() / 1000 <= currentTime) {
                    nextBillCal.add(Calendar.WEEK_OF_YEAR, 1);
                }
                break;
            case 5: // 日
                while (nextBillCal.getTimeInMillis() / 1000 <= currentTime) {
                    nextBillCal.add(Calendar.DAY_OF_MONTH, 1);
                }
                break;
            default:
                return startTime;
        }

        return nextBillCal.getTimeInMillis() / 1000;
    }

    /**
     * 计算账单周期开始时间（当前周期的开始时间）
     *
     * @param subscription 订阅信息
     * @param currentTime 当前时间
     * @return 当前账单周期开始时间
     */
    private long calculateBillPeriodStartTime(FgRemindPo subscription, long currentTime) {
        long startTime = subscription.getStartTime();
        int cycle = subscription.getRemindCycle();

        // 永久订阅使用开始时间作为账单周期开始时间
        if (cycle == 0) {
            return startTime;
        }

        Calendar startCal = Calendar.getInstance();
        startCal.setTimeInMillis(startTime * 1000);

        Calendar periodStartCal = Calendar.getInstance();
        periodStartCal.setTimeInMillis(startTime * 1000);

        switch (cycle) {
            case 1: // 年
                while (periodStartCal.getTimeInMillis() / 1000 + 365 * 24 * 60 * 60 <= currentTime) {
                    periodStartCal.add(Calendar.YEAR, 1);
                }
                break;
            case 3: // 月
                while (periodStartCal.getTimeInMillis() / 1000 + 30 * 24 * 60 * 60 <= currentTime) {
                    periodStartCal.add(Calendar.MONTH, 1);
                }
                break;
            case 4: // 周
                while (periodStartCal.getTimeInMillis() / 1000 + 7 * 24 * 60 * 60 <= currentTime) {
                    periodStartCal.add(Calendar.WEEK_OF_YEAR, 1);
                }
                break;
            case 5: // 日
                while (periodStartCal.getTimeInMillis() / 1000 + 24 * 60 * 60 <= currentTime) {
                    periodStartCal.add(Calendar.DAY_OF_MONTH, 1);
                }
                break;
            default:
                return startTime;
        }

        return periodStartCal.getTimeInMillis() / 1000;
    }

    /**
     * 获取今天开始时间（00:00:00）
     *
     * @return 今天开始时间戳（秒）
     */
    private long getTodayStartTime() {
        Calendar today = Calendar.getInstance();
        today.set(Calendar.HOUR_OF_DAY, 0);
        today.set(Calendar.MINUTE, 0);
        today.set(Calendar.SECOND, 0);
        today.set(Calendar.MILLISECOND, 0);
        return today.getTimeInMillis() / 1000;
    }

    /**
     * 获取周期描述
     *
     * @param cycle 周期代码
     * @return 周期描述
     */
    private String getCycleDescription(Integer cycle) {
        if (cycle == null) {
            return "未设置";
        }
        switch (cycle) {
            case 0:
                return "永久";
            case 1:
                return "年";
            case 3:
                return "月";
            case 4:
                return "周";
            case 5:
                return "日";
            default:
                return "未知(" + cycle + ")";
        }
    }

    /**
     * 测试方法：验证账单生成逻辑
     * 可以通过接口调用来测试定时任务的逻辑
     *
     * @param userId 指定用户ID进行测试，为空则测试所有用户
     * @return 测试结果信息
     */
    @Test
    public String testBillGeneration(String userId) {
        log.info("开始测试账单生成逻辑，用户ID: {}", userId);
        StringBuilder result = new StringBuilder();

        try {
            long currentTime = TimeUtils.getCurrentTime();
            result.append("当前时间: ").append(currentTime).append("\n");

            // 查询订阅
            FgRemindPo queryParam = new FgRemindPo();
            queryParam.setStatus(1);
            if (userId != null && !userId.trim().isEmpty()) {
                queryParam.setUserId(userId);
            }

            List<FgRemindPo> subscriptions = fgRemindDao.filterFgRemindList(queryParam);
            result.append("找到订阅数量: ").append(subscriptions != null ? subscriptions.size() : 0).append("\n\n");

            if (subscriptions == null || subscriptions.isEmpty()) {
                result.append("没有找到符合条件的订阅");
                return result.toString();
            }

            int needGenerateCount = 0;
            int skipCount = 0;

            for (FgRemindPo subscription : subscriptions) {
                result.append("订阅: ").append(subscription.getProductName())
                      .append(" (").append(subscription.getId()).append(")\n");
                result.append("  用户ID: ").append(subscription.getUserId()).append("\n");
                result.append("  周期: ").append(getCycleDescription(subscription.getRemindCycle())).append("\n");
                result.append("  开始时间: ").append(subscription.getStartTime()).append("\n");
                result.append("  结束时间: ").append(subscription.getEndTime()).append("\n");

                // 检查是否需要生成账单
                boolean shouldGenerate = shouldGenerateBill(subscription, currentTime);
                result.append("  是否需要生成账单: ").append(shouldGenerate).append("\n");

                if (shouldGenerate) {
                    needGenerateCount++;
                    long billPeriodStartTime = calculateBillPeriodStartTime(subscription, currentTime);
                    result.append("  账单周期开始时间: ").append(billPeriodStartTime).append("\n");

                    // 检查是否已存在账单记录
                    boolean hasExisting = hasBillRecordForPeriod(subscription, billPeriodStartTime);
                    result.append("  是否已存在账单记录: ").append(hasExisting).append("\n");
                } else {
                    skipCount++;
                    // 分析跳过的原因
                    String skipReason = analyzeSkipReason(subscription, currentTime);
                    result.append("  跳过原因: ").append(skipReason).append("\n");
                }

                result.append("\n");
            }

            result.append("总结:\n");
            result.append("  需要生成账单的订阅: ").append(needGenerateCount).append("\n");
            result.append("  跳过的订阅: ").append(skipCount).append("\n");

        } catch (Exception e) {
            log.error("测试账单生成逻辑时发生错误", e);
            result.append("测试过程中发生错误: ").append(e.getMessage());
        }

        return result.toString();
    }

    /**
     * 分析订阅跳过生成账单的原因
     *
     * @param subscription 订阅信息
     * @param currentTime 当前时间
     * @return 跳过原因
     */
    private String analyzeSkipReason(FgRemindPo subscription, long currentTime) {
        if (subscription.getRemindCycle() == null) {
            return "未设置提醒周期";
        }

        if (subscription.getStartTime() == null || subscription.getStartTime() > currentTime) {
            return "订阅还未开始";
        }

        if (subscription.getEndTime() != null && subscription.getEndTime() < currentTime) {
            return "订阅已结束";
        }

        if (subscription.getRemindCycle() == 0) {
            boolean hasExisting = hasBillRecordForPeriod(subscription, subscription.getStartTime());
            if (hasExisting) {
                return "永久订阅已生成过账单";
            } else {
                return "永久订阅但不在今天生成";
            }
        }

        long nextBillTime = calculateNextBillTime(subscription, currentTime);
        long todayStart = getTodayStartTime();
        long tomorrowStart = todayStart + 24 * 60 * 60;

        if (nextBillTime < todayStart) {
            return "下次账单时间已过期";
        } else if (nextBillTime >= tomorrowStart) {
            return "下次账单时间还未到 (下次时间: " + nextBillTime + ")";
        } else {
            boolean hasExisting = hasBillRecordForPeriod(subscription, nextBillTime);
            if (hasExisting) {
                return "该周期已存在账单记录";
            } else {
                return "未知原因";
            }
        }
    }
}

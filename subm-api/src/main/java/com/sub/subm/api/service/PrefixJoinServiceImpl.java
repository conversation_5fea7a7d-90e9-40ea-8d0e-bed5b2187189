package com.sub.subm.api.service;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sub.common.exception.BaseException;
import com.sub.common.utils.StringUtils;
import com.sub.subm.api.entity.vo.filterRemindVO;
import com.sub.subm.dal.constant.SettingResultConstant;
import com.sub.subm.dal.entity.generate.product.ProductCategoryPo;
import com.sub.subm.dal.entity.generate.product.ProductPo;
import com.sub.subm.dal.entity.generate.product.RegionPo;
import com.sub.subm.dal.service.SettingResultServiceImpl;

/**
 * 特殊数据 返回前端 特殊处理, 如 图片前缀 等, 或考虑    @JsonSerialize 注解方式, 表名等
 */
@Service
public class PrefixJoinServiceImpl {

    private Logger log = LoggerFactory.getLogger(PrefixJoinServiceImpl.class);

    @Autowired
    private SettingResultServiceImpl settingResultService;

    /**
     * 图片前缀
     */
    public List<filterRemindVO> prefixImgRemind(List<filterRemindVO> list) {
        if (list == null || list.size() == 0) { return list;}
        try {
            String imgAddress = settingResultService.getAppointNameStr(SettingResultConstant.IMAGE_SUFFIX_URI);
            list.forEach(remind -> {
                if (StringUtils.isNotEmpty(remind.getImg()) && !remind.getImg().startsWith("http")) {
                    remind.setImg(imgAddress + remind.getImg());
                }
            });
        } catch (BaseException e) {
            log.error("获取图片前缀失败", e);
        }
        return list;
    }

    public List<ProductPo> prefixImageProduct(List<ProductPo> list) {
        if (list == null || list.size() == 0) { return list;}
        try {
            String imgAddress = settingResultService.getAppointNameStr(SettingResultConstant.IMAGE_SUFFIX_URI);
            list.forEach(product -> {
                if (StringUtils.isNotEmpty(product.getIconUrl()) && !product.getIconUrl().startsWith("http")) {
                    product.setIconUrl(imgAddress + product.getIconUrl());
                }
            });
        } catch (BaseException e) {
            log.error("获取图片前缀失败", e);
        }
        return list;
    }

    public List<RegionPo> prefixImageRegion(List<RegionPo> list) {
        if (list == null || list.size() == 0) { return list;}
        try {
            String imgAddress = settingResultService.getAppointNameStr(SettingResultConstant.IMAGE_SUFFIX_URI);
            list.forEach(region -> {
                if (StringUtils.isNotEmpty(region.getIconUrl()) && !region.getIconUrl().startsWith("http")) {
                    region.setIconUrl(imgAddress + region.getIconUrl());
                }
            });
        } catch (BaseException e) {
            log.error("获取图片前缀失败", e);
        }
        return list;
    }

    public List<ProductCategoryPo> prefixImageCategory(List<ProductCategoryPo> list) {
        if (list == null || list.size() == 0) { return list; }
        try {
            String imgAddress = settingResultService.getAppointNameStr(SettingResultConstant.IMAGE_SUFFIX_URI);
            list.forEach(category -> {
                if (StringUtils.isNotEmpty(category.getIconUrl()) && !category.getIconUrl().startsWith("http")) {
                    category.setIconUrl(imgAddress + category.getIconUrl());
                }
            });
        } catch (BaseException e) {
            log.error("获取图片前缀失败", e);
        }
        return list;
    }


}

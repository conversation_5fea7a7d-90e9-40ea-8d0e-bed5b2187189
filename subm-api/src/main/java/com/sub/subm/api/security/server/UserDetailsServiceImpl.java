package com.sub.subm.api.security.server;

import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSONObject;
import com.sub.common.constant.CacheConstants;
import com.sub.common.core.redis.RedisCache;
import com.sub.common.exception.BaseException;
import com.sub.common.utils.auth.AuthUtils;
import com.sub.framework.security.context.AuthenticationContextHolder;
import com.sub.subm.api.entity.vo.LoginUser;
import com.sub.subm.api.security.custom.CustomAuthenticationToken;
import com.sub.subm.api.service.account.VerifyCodeService;
import com.sub.subm.dal.constant.SettingResultConstant;
import com.sub.subm.dal.dao.user.UserDao;
import com.sub.subm.dal.entity.bo.LoginErrBo;
import com.sub.subm.dal.entity.generate.user.UserPo;
import com.sub.subm.dal.service.SettingResultServiceImpl;

import lombok.SneakyThrows;

/**
 * 用户验证处理
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {
    private static final Logger log = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

    @Autowired
    private UserDao userDao;
    @Autowired
    private VerifyCodeService verifyCodeService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private SettingResultServiceImpl settingResultServiceImpl;


    @SneakyThrows
    @Override
    public UserDetails loadUserByUsername(String username) {
        CustomAuthenticationToken authentication = (CustomAuthenticationToken) AuthenticationContextHolder.getContext();
        Integer channel = authentication.getChannel();
        Integer type = authentication.getType();
        UserPo userPo = getUserPo(username, channel);
        String pwd = authentication.getPwd();

        switch (type) {
            case 0:
                // 登录账号 和 密码
                validatePwd(userPo, pwd);
                return extraValid(userPo); // 账号和密码需验证用户是否开启 2fa 验证方式
            case 1:
                // 验证码登录方式
                verifyCodeLogin(username, channel, pwd);
                break;
            case 2:
                // 2向验证码登录
                break;
            case 3:
                // 三方登录 暂无其他逻辑
                break;
        }
        return createLoginUser(userPo);
    }

    public UserPo getUserPo(String username, Integer channel) throws BaseException {
        UserPo userPo = userDao.userLogin(username, channel);
        if (userPo == null || userPo.getEnable() == 0) {
            log.warn("登录账号: {} 渠道: {} 不存在", username, channel);
            throw new BaseException(-100, "user.not.exists");
        } else if (userPo.getEnable() == 2) {
            log.warn("登录用户: {} 被停用", userPo.getId());
            throw new BaseException(-100, "user.blocked");
        }
        return userPo;
    }

    /**
     * 密码验证
     * @param userPo
     * @param password
     */
    public void validatePwd(UserPo userPo, String password) throws BaseException {
        String redisKey = CacheConstants.PWD_ERROR_COUNT + userPo.getId();
        Integer retryCount = redisCache.getCacheObject(redisKey);
        if (retryCount == null) {
            retryCount = 0;
        }
        LoginErrBo loginErrBo = JSONObject.parseObject(settingResultServiceImpl.getAppointNameStr(SettingResultConstant.FAIL_LOGIN_TIME), LoginErrBo.class);
        if (retryCount >= loginErrBo.getCount()) {
            log.warn("密码错误已超过 系统能接受的最大次数");
            throw new BaseException(-102, "user.password.retry.limit.exceed");
        }
        if (!userPo.getPassword().equals(AuthUtils.md5Hex(password, userPo.getSalt()))) {
            retryCount = retryCount + 1;
            log.warn("登录账户: {} 密码不正确, 当前已重试: {}次", userPo.getId(), retryCount);
            redisCache.setCacheObject(redisKey, retryCount, loginErrBo.getDuration(), TimeUnit.values()[loginErrBo.getTimeEnum()]);
            throw new BaseException(-101, "user.password.not.match");
        } else {
            redisCache.deleteObject(redisKey);
        }
    }

    /**
     * 是否需要额外验证, 仅 账号 + 密码登录 有效
     * @param userPo
     */
    public UserDetails extraValid(UserPo userPo) {
//        UserSettingBo verifyTwo = JSONObject.parseObject(userPo.getUserSetting(), UserSettingBo.class);
//        Integer twoEnable = 0;
//        if (verifyTwo.getTwoVerify() == 1) {
//            twoEnable = 1;
//        }
        return new LoginUser(userPo.getId(), userPo);
    }

    /**
     * 验证码登录
     */
    public void  verifyCodeLogin(String account, Integer channel, String pwd) throws BaseException {
        String redisKey = CacheConstants.VERIFY  + channel + ":" +
                VerifyCodeService.MailSubject.LOGIN_VERIFY.ordinal() + ":" + account;
        verifyCodeService.verifyCode(redisKey, pwd);
    }

    /**
     * 登录成功 后用户登录信息
     * @param userPo
     * @return
     */
    public UserDetails createLoginUser(UserPo userPo) {
        return new LoginUser(userPo.getId(), userPo);
    }
}

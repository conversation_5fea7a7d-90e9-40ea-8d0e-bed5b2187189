package com.sub.subm.api.service.assistant.tools;

import org.springframework.stereotype.Component;

import com.sub.common.utils.StringUtils;
import com.sub.subm.api.service.UserServiceImpl;
import com.sub.subm.api.service.assistant.context.UserContextManager;
import com.sub.subm.api.service.assistant.model.dto.ToolCallResponse;
import com.sub.subm.dal.entity.generate.user.UserPo;

import dev.langchain4j.agent.tool.P;
import dev.langchain4j.agent.tool.Tool;
import dev.langchain4j.agent.tool.ToolMemoryId;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户相关工具类
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserTool {

    private final UserServiceImpl userService;
    private final UserContextManager userContextManager;

    @Tool("获取当前用户的基本信息，包括用户ID、姓名、邮箱等")
    public ToolCallResponse getUserInfo(@ToolMemoryId Object memoryId) throws Exception {
        long startTime = System.currentTimeMillis();
        try {
            // 从用户上下文管理器获取用户信息
            String userId = userContextManager.getUserId(memoryId);
            UserPo userPo = userService.getByIdUserPo(userId);
            if (userPo == null) {
                throw new Exception("暂无用户信息");
            }

            ToolCallResponse toolCallResponse = ToolCallResponse.builder()
                    .toolName("getUserInfo")
                    .toolResult(userPo)
                    .toolResultDescription("查询用户信息，包括用户ID、姓名、邮箱、手机号、描述")
                    .toolStatus("success")
                    .executionTime(System.currentTimeMillis() - startTime)
                    .toolVersion("1.0")
                    .build();

            return toolCallResponse;
        } catch (Exception e) {
            log.error("获取用户信息异常", e);
            throw new Exception(e.getMessage());
        }
    }

    @Tool("修改当前用户个人信息，如果用户未提供信息，则不修改")
    public ToolCallResponse updateUserInfo(@ToolMemoryId Object memoryId,
            @P(value = "用户名", required = false) String userName,
            @P(value = "邮箱", required = false) String email,
            @P(value = "手机号", required = false) String phone,
            @P(value = "描述", required = false) String motto) throws Exception {

        long startTime = System.currentTimeMillis();
        try {
            userContextManager.validateUser(memoryId);
            String userId = userContextManager.getUserId(memoryId);
            UserPo userPo = userService.getByIdUserPo(userId);
            if (userPo == null) {
                throw new Exception("暂无用户信息");
            }
            if (StringUtils.isNotEmpty(userName)) {
                userPo.setName(userName);
            }
            if (StringUtils.isNotEmpty(email)) {
                userPo.setEmail(email);
            }
            if (StringUtils.isNotEmpty(phone)) {
                userPo.setPhone(phone);
            }
            if (StringUtils.isNotEmpty(motto)) {
                userPo.setMotto(motto);
            }
            userService.updateUser(userPo);

            ToolCallResponse toolCallResponse = ToolCallResponse.builder()
                    .toolName("updateUserInfo")
                    .toolResult(userPo)
                    .toolResultDescription("修改用户信息成功，包括用户ID、姓名、邮箱、手机号、描述")
                    .toolStatus("success")
                    .executionTime(System.currentTimeMillis() - startTime)
                    .toolVersion("1.0")
                    .build();
            return toolCallResponse;
        } catch (Exception e) {
            log.error("修改用户信息异常", e);
            throw new Exception(e.getMessage());
        }
    }

    // @Tool("修改当前用户密码，无需验证旧密码，如用户未提供新密码，则提醒用户输入新密码")
    // public String updateUserPasswordWithoutOldPassword(
    // @ToolMemoryId Object memoryId,
    // @P("新密码") String newPassword) throws Exception {
    // if (StringUtils.isEmpty(newPassword)) {
    // throw new Exception("请输入新密码");
    // }

    // try {
    // // 验证用户权限并获取用户ID
    // userContextManager.validateUser(memoryId);
    // String userId = userContextManager.getUserId(memoryId);

    // UserPo userPo = userService.getByIdUserPo(userId);
    // if (userPo == null) {
    // throw new Exception("修改失败，用户不存在");
    // }

    // UserPo newUserPo = new UserPo();
    // newUserPo.setId(userId);
    // newUserPo.setSalt(StringUtils.verifySalt(6));
    // newUserPo.setPassword(AuthUtils.md5Hex(newPassword, newUserPo.getSalt()));
    // userService.updateUser(newUserPo);
    // return "密码修改成功！请使用新密码重新登录。";
    // } catch (Exception e) {
    // throw new Exception("修改密码失败：" + e.getMessage());
    // }
    // }

    // @Tool("修改当前用户密码，需要验证旧密码，如用户未提供旧密码，则提醒用户输入旧密码")
    // public String updateUserPasswordWithOldPassword(
    // @ToolMemoryId Object memoryId,
    // @P("旧密码") String oldPassword,
    // @P("新密码") String newPassword) throws Exception {

    // if(StringUtils.isEmpty(oldPassword)){
    // throw new Exception("提醒用户输入旧密码");
    // }
    // if(StringUtils.isEmpty(newPassword)){
    // throw new Exception("提醒用户输入新密码");
    // }

    // try {
    // // 验证用户权限并获取用户ID
    // userContextManager.validateUser(memoryId);
    // String userId = userContextManager.getUserId(memoryId);

    // log.info("修改用户密码, memoryId: {}, userId: {}", memoryId, userId);

    // UserPo userPo = userService.getByIdUserPo(userId);
    // if (userPo == null) {
    // throw new Exception("修改失败，用户不存在");
    // }

    // if (StringUtils.isNotEmpty(userPo.getPassword())) {
    // String salt = userPo.getSalt();
    // String encryptedOldPassword = AuthUtils.md5Hex(oldPassword, salt);

    // if (!encryptedOldPassword.equals(userPo.getPassword())) {
    // throw new Exception("旧密码错误，请重新输入旧密码");
    // }
    // }

    // UserPo newUserPo = new UserPo();
    // newUserPo.setId(userId);
    // newUserPo.setSalt(StringUtils.verifySalt(6));
    // newUserPo.setPassword(AuthUtils.md5Hex(newPassword, newUserPo.getSalt()));

    // userService.updateUser(newUserPo);

    // log.info("用户密码修改成功, memoryId: {}, userId: {}", memoryId, userId);
    // return "密码修改成功！请使用新密码重新登录。";

    // } catch (IllegalStateException e) {
    // return "修改密码失败: " + e.getMessage();
    // } catch (BaseException e) {
    // log.error("修改用户密码失败, error: {}", e.getMessage(), e);
    // return "修改密码失败：" + e.getMessage();
    // } catch (Exception e) {
    // log.error("修改用户密码异常", e);
    // return "修改密码失败：系统异常，请稍后重试";
    // }
    // }

    // @Tool("获取当前用户的订阅统计信息")
    // public String getUserSubscriptionStats() {
    // try {
    // SessionContext.validateCurrentUser();
    // String userId = SessionContext.getCurrentUserId();

    // log.info("获取用户订阅统计, userId: {}", userId);

    // // TODO: 实现订阅统计逻辑
    // return String.format("用户 %s 的订阅统计: 总订阅数: 5, 活跃订阅: 3, 已取消: 2, 月度总费用: $45.97",
    // userId);
    // } catch (IllegalStateException e) {
    // return "获取订阅统计失败: " + e.getMessage();
    // } catch (Exception e) {
    // log.error("获取用户订阅统计异常", e);
    // return "获取订阅统计失败: 系统异常";
    // }
    // }
}

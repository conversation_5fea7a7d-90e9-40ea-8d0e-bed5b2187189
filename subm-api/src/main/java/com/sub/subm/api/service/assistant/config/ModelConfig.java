package com.sub.subm.api.service.assistant.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.model.openai.OpenAiStreamingChatModel;

/**
 * Lang<PERSON>hain4j模型配置
 */
@Configuration
public class ModelConfig {

    private final String openAiApiBaseUrl = "https://api.siliconflow.cn/v1";

    private final String openAiApiKey = "sk-clceszrcsyepqekxlxglkevropjtbascdsxturvmjcysenfu";

    private final String openAiModel = "moonshotai/Kimi-K2-Instruct";

    @Bean
    public OpenAiStreamingChatModel openAiStreamingChatModel() {
        return OpenAiStreamingChatModel.builder()
                .baseUrl(openAiApiBaseUrl)
                .apiKey(openAiApiKey)
                .modelName(openAiModel)
                .temperature(0.7)
                .maxTokens(2000)
                .logRequests(true)
                .logResponses(true)
                .build();
    }

    @Bean
    public OpenAiChatModel openAiChatModel(){
        return OpenAiChatModel.builder()
                .baseUrl(openAiApiBaseUrl)
                .apiKey(openAiApiKey)
                .modelName(openAiModel)
                .temperature(0.7)
                .maxTokens(2000)
                .logRequests(true)
                .logResponses(true)
                .build();
    }
}

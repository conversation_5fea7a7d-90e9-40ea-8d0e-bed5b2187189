package com.sub.subm.api.entity.dto;


import java.io.Serializable;

import org.hibernate.validator.constraints.Range;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

public  class UserLoginDTOs {


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(description = "登录模块 基础 公共数据")
    public static class CommonDTO implements Serializable {
        @Schema(description = "账号",  required = true)
        @NotBlank(message = "账号不能为空")
        private String account;
        @Schema(description = "渠道: [0: 邮箱 1: 手机号 2: google账号 3:微信账号 4: 苹果账号] (发送验证码只可(0 和 1)", required = true)
        @NotNull(message = "重要区分数据不能为空")
        private Integer channel;
    }



    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(description = "发送验证码信息")
    public static class SendVerifyDTO extends CommonDTO implements Serializable {
        @Schema(description = "验证类别 [0: 注册 1:找回密码 2: 修改密码 3: 账号绑定 4:登录验证 5:账号验证]", required = true)
        @NotNull(message = "类别信息不能为空")
        private Integer type;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(description = "验证信息")
    public static class VerifyPwdDTO extends CommonDTO implements Serializable{
        @Schema(description = "验证码", required = true)
        private String verifyCode;
        @Schema(description = "验证类型", required = true)
        private String type;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(description = "忘记密码")
    public static class ForPwdDTO implements Serializable {
        @Schema(description = "账号", required = true)
        private String account;
        @Schema(description = "渠道: [0: 邮箱 1: 手机号 ]", required = true)
        private Integer channel;
        @Schema(description = "验证码", required = true)
        private String verifyCode;
        @Schema(description = "新密码", required = true)
        private String pwd;
    }



    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(description = "用户登录")
    public static class UserLoginDTO extends CommonDTO  implements Serializable {
        @Schema(description = "仅验证码 登录方式可选: 类别[0: 密码 1: 验证码]", required = true)
        private Integer type;
        @Schema(description = "登录密码", required = true)
        @NotBlank(message = "密码/验证码 不能为空")
        private String pwd;
        @Schema(description = "是否是第二次操作", required = true)
        @NotNull(message = "重要验证参数不能为空")
        private Boolean operation;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(description = "三方账号登录")
    public static class AuthRegisterDTO implements Serializable {
        @Schema(description = "三方唯一id")
        @NotBlank(message = "三方唯一id 不能为空")
        private String uuid;
        @Schema(description = "账号")
        @NotBlank(message = "账号不能为空")
        private String account;
        @Schema(description = "渠道 来源[0: 邮箱 1: 手机号 2: google账号 3:微信账号 4: 苹果账号]")
        @Range(min = 1, max = 5, message = "账号来源区间在 1 - 5")
        @NotNull(message = "来源不能为空")
        private Integer channel;
        @Schema(description = "用户名称")
        private String name;
        @Schema(description = "用户头像")
        private String avatar;

        @Schema(description = "是否注册新的或则关联账号,  默认传0,  1: 新的 2: 关联", required = true)
        @NotBlank(message = "重要参数不能为空")
        private Integer relevance;
    }



    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(description = "2fa 验证登录")
    public static class TwoLoginDTO implements Serializable {
        @Schema(description = "用户id", required = true)
        @NotBlank(message = "用户id 不能为空")
        private String userId;
        @Schema(description = "验证码", required = true)
        @NotBlank(message = "验证码数据不能为空")
        private String pwd;
    }



    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(description = "用户注册")
    public static class RegisterTDO extends CommonDTO  implements Serializable{
        @Schema(description = "用户名称", required = true)
//        @NotBlank(message =  "名称不能为空")
//        @Size(min = 1, max=10, message = "用户名称长度必须在1 到 10 个字符 之间")
        private String name;
        @Schema(description = "用户设置的密码 [长度必须在6 到 20 个字符 之间]", required = true)
        @NotBlank(message =  "密码不能为空")
        @Size(min = 6, max=20, message = "用户名称长度必须在1 到 20 个字符 之间")
        private String pwd;
        @Schema(description = "验证码", required = true)
        @NotBlank(message = "验证码不能为空")
        private String verifyCode;
        @Schema(description = "描述")
        private String motto;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(description = "修改密码")
    public static class UpdateAccountPwd implements Serializable {
        @Schema(description = "旧密码", required = true)
        private String oldPwd;
        @Schema(description = "新密码", required = true)
        @NotBlank(message =  "新密码不能为空")
        private String newPwd;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(description = "修改用户基础信息")
    public static class UserBasicInfo implements Serializable {
        @Schema(description = "名称")
        @NotBlank(message = "名称不能为空")
        private String name;
        @Schema(description = "个人描述")
        private String motto;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(description = "绑定账号 或 解绑")
    public static class BindAccountDTO implements Serializable{
        @Schema(description = "绑定账号")
        @NotBlank(message = "账号不能为空")
        private String account;
        @Schema(description = "uuid, 三方登录必传")
        private String uuid;
        @Schema(description = "渠道 [0: 邮箱 1: 手机号 2: google账号 3:微信账号 4: 苹果账号]", required = true)
        @NotBlank(message = "渠道不能为空")
        private Integer channel;
        @Schema(description = "验证码,  绑定三方登录时, 此参数可不管")
        private String verifyCode;
    }



}

package com.sub.subm.api.security.filter;


import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.servlet.HandlerExceptionResolver;

import com.sub.common.manager.AsyncManager;
import com.sub.common.utils.StringUtils;
import com.sub.framework.jwt.exception.TokenException;
import com.sub.subm.api.config.SecurityConfig;
import com.sub.subm.api.entity.vo.LoginUser;
import com.sub.subm.api.manager.AsyncFactory;
import com.sub.subm.api.security.SecurityUtils;
import com.sub.subm.api.security.TokenUtil;
import com.sub.subm.api.security.custom.CustomAuthenticationToken;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;


/**
 * token过滤器 验证token有效性
 */
@Component
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {

    @Autowired
    private TokenUtil tokenUtil;
    @Autowired
    @Qualifier("handlerExceptionResolver")
    private HandlerExceptionResolver resolver;


    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        String requestUrl = request.getRequestURI();

        // 检查是否为白名单URL（支持通配符匹配）
        boolean isWhitelistUrl = isUrlInWhitelist(requestUrl);
        boolean isSpecialUrl = SecurityConfig.SPECIAL_URL_LIST.contains(requestUrl);

        // 只有不在白名单中的URL或特殊URL才需要token验证
        if (!isWhitelistUrl || isSpecialUrl) {
            LoginUser loginUser = new LoginUser();
            // 特殊api(如: 添加参数 即可鉴权的) 手动捕获异常
            if (isSpecialUrl) {
                try {
                    loginUser = tokenUtil.getToken(request);
                } catch (TokenException e) {
                   loginUser = null;
                }
            } else {
                try {
                    loginUser = tokenUtil.getToken(request);
                    // String token = tokenUtil.generateToken(loginUser); // 假设tokenUtil有一个generateToken方法
                    // response.setHeader("Authorization", "Bearer " + token);
                } catch (TokenException e) {
                    resolver.resolveException(request, response, null, e);
                    return;
                }
            }
            if (StringUtils.isNotNull(loginUser) && StringUtils.isNull(SecurityUtils.getAuthentication())) {
                CustomAuthenticationToken customAuthenticationToken = new CustomAuthenticationToken(loginUser, null, loginUser.getAuthorities());
                customAuthenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(customAuthenticationToken);
            }
        }
//        异步工厂记录请求数据
//        AsyncManager.me().execute(AsyncFactory.recordRequestData(request));
        filterChain.doFilter(request, response);
    }

    /**
     * 检查URL是否在白名单中（支持通配符匹配）
     */
    private boolean isUrlInWhitelist(String requestUrl) {
        for (String pattern : SecurityConfig.NOT_AUTO_URL_LIST) {
            if (pattern.endsWith("/**")) {
                // 处理通配符匹配
                String prefix = pattern.substring(0, pattern.length() - 3);
                if (requestUrl.startsWith(prefix)) {
                    return true;
                }
            } else if (pattern.equals(requestUrl)) {
                // 精确匹配
                return true;
            }
        }
        return false;
    }
}

package com.sub.subm.api;

import java.util.HashMap;
import java.util.Map;

import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.EnvironmentPBEConfig;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import com.alibaba.fastjson2.JSONObject;
import com.sub.subm.api.service.ExchangRateService;
import com.sub.subm.api.service.RemindServiceImpl;
import com.sub.subm.api.task.scheduled.ExchangeRateScheduledTask;
import com.sub.subm.dal.dao.remind.FgRemindInfoDao;
import com.sub.subm.dal.entity.generate.remind.FgRemindInfoPo;

@SpringBootTest
public class SubMApiTestApplication {

    @Autowired
    private ExchangRateService exchangRateService;
    @Autowired
    private RemindServiceImpl remindService;
    @Autowired
    private FgRemindInfoDao fgRemindInfoDao;

    @Test
    public void testExchangeRate() throws Exception {
        FgRemindInfoPo fgRemindInfoPo = new FgRemindInfoPo();
        Map<String, Object> map = new HashMap<>();
        map.put("groupBy", "category");
        fgRemindInfoPo.setParams(map);
        System.out.println(JSONObject.toJSONString(fgRemindInfoDao.filterFgRemindInfoList(fgRemindInfoPo)));
    }

    // 解密
    @Test
    public void decrypt() {

        StandardPBEStringEncryptor encryptor = new StandardPBEStringEncryptor();
        EnvironmentPBEConfig config = new EnvironmentPBEConfig();

        config.setAlgorithm("PBEWithMD5AndDES");
        config.setPassword("amsubfg");
        encryptor.setConfig(config);

        // 解密
        System.out.println(encryptor.decrypt("x80xPqqjocB0OhJIhvIjleQ5PgajYO18"));
    }

    // 加密
    @Test
    public void encryptor() {
        StandardPBEStringEncryptor encryptor = new StandardPBEStringEncryptor();
        EnvironmentPBEConfig config = new EnvironmentPBEConfig();

        config.setAlgorithm("PBEWithMD5AndDES");
        config.setPassword("amsubfg");
        encryptor.setConfig(config);

        // 加密
        System.out.println(encryptor.encrypt("zhyzhy356789"));
    }

    @Autowired
    private ExchangeRateScheduledTask exchangeRateScheduledTask;

    @Test
    public void testExchangeRateScheduledTask() {
        exchangeRateScheduledTask.fetchExchangeRates();
    }
}

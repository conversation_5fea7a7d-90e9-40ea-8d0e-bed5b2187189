package com.sub.framework.jwt;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.crypto.SecretKey;

import com.sub.common.enums.TimeEnum;
import com.sub.common.exception.BaseException;
import com.sub.common.utils.ServletUtils;
import com.sub.common.utils.StringUtils;
import com.sub.common.utils.TimeUtils;
import com.sub.framework.jwt.constant.OneTokenConfig;
import com.sub.framework.jwt.exception.LoginTokenEnum;
import com.sub.framework.jwt.exception.TokenException;

import eu.bitwalker.useragentutils.UserAgent;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@RequiredArgsConstructor
public class TokenBasic {

    private final OneTokenConfig oneTokenConfig;

    protected static long REPLACETIME;
    protected static long VALIDTIME;
    protected static boolean isConfigToken;
    protected static TimeEnum LONGTIMEENUM;
    protected static TimeEnum SHORTTIMEENUM;

    protected static SecretKey AUTH_KEY;


    @PostConstruct
    public void initGetTokenConfig() throws BaseException {
        tokenTimeEnum(oneTokenConfig);
    }

    private synchronized void tokenTimeEnum(OneTokenConfig oneTokenConfig) throws BaseException {
        if (oneTokenConfig == null) {
            log.info("未能获取到有效配置token信息!");
            isConfigToken = true;
            return;
        }
        oneTokenConfig = oneTokenConfig;
        LONGTIMEENUM = TimeEnum.getTimeTypeEnum(oneTokenConfig.getLongType());
        SHORTTIMEENUM = TimeEnum.getTimeTypeEnum(oneTokenConfig.getShortType());
        REPLACETIME = TimeUtils.getTimeEnumSecond(oneTokenConfig.getReplaceTimeout(), TimeEnum.DAY);
        VALIDTIME = TimeUtils.getTimeEnumSecond(oneTokenConfig.getLongTimeout(), LONGTIMEENUM);


        String secret = oneTokenConfig.getAuthKey();
        byte[] encodedKey = secret.getBytes(StandardCharsets.UTF_8);
        byte[] keyBytes = Arrays.copyOf(encodedKey, 64);
        AUTH_KEY = Keys.hmacShaKeyFor(keyBytes);
    }





    /**
     * 将获取的token, 进行解析,
     **/
    protected Claims getClaimFromToken(String token) {
        try {
            return Jwts.parser()
                    .verifyWith(AUTH_KEY)
                    .build()
                    .parseSignedClaims(token)
                    .getPayload();
        } catch (ExpiredJwtException e) {
            return e.getClaims();
        }
    }

    /**
     * 私有方法 生成客户端,所需的短token
     **/
    protected String generateShortToken(String client, String uuid, String user, String subject) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("user", user);
        claims.put("client", client);
        claims.put("refreshToken", uuid);
        Date createDate = new Date();
        Date expirationDate = new Date(
                TimeUtils.getAddTimeStamp(
                        createDate,
                        TimeUtils.getTimeEnumSecond(oneTokenConfig.getShortTimeout(), SHORTTIMEENUM)));
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(createDate)
                .setExpiration(expirationDate)
                .signWith(AUTH_KEY, SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 验证token 是否合法!!!
     **/
    protected LoginTokenEnum verifyLoginTokenInfo(String token, String refreshToken,
                                                String client, String user) {
        if (StringUtils.isEmpty(refreshToken)) {
            log.error("未能获取该客户端有效的token信息, {} 未能获取到长token {}", user, refreshToken);
            return LoginTokenEnum.LOGIN_INFO_ERROR;
        }
        // 解析请求时客户端携带的短token
        Claims claims = null;
        try {
            claims = getClaimFromToken(token);
        } catch (ExpiredJwtException e) {
            log.warn("token 信息已过期");
            claims = e.getClaims();
        }
        String tokenRefreshToken = claims.get("refreshToken").toString();
        if (!tokenRefreshToken.equals(refreshToken)) {
            log.warn("账户已在其他地方登录, 短token 值与 长token值不一致");
            return LoginTokenEnum.LOGIN_IN_REST_LOGIN;
        }
        if (claims.getExpiration().before(new Date())) {
            log.warn("token 信息短过期");
            return LoginTokenEnum.LOGIN_INFO_PASTDUE;
        }
        String tokenUser = claims.get("mapper/user").toString();
        if (StringUtils.isEmpty(user) || !user.equals(tokenUser)) {
            log.error("token 用户信息不一致");
            return LoginTokenEnum.LOGIN_INFO_ERROR;
        }
        String tokenClient = claims.get("client").toString();
        if (StringUtils.isEmpty(client) || !client.equals(tokenClient)) {
            log.error("token 信息客户端信息不符合");
            return LoginTokenEnum.LOGIN_INFO_ERROR;
        }
        return LoginTokenEnum.LOGIN_SUCCESS;
    }

    /**
     * 检查客户端是否符合系统
     * @param client
     */
    protected void checkClient(String client) throws TokenException {
        UserAgent userAgent = UserAgent.parseUserAgentString(ServletUtils.getRequest().getHeader("User-Agent"));
        String useClient = userAgent.getOperatingSystem().getName();
//        if (!client.equals(useClient)) {
//            log.warn("客户端型号 不一致, token 检查出现异常, 适用的{} token 客户端: {}", useClient, client);
//            throw new TokenException(LoginTokenEnum.LOGIN_INFO_ERROR);
//        }
    }

    protected String getHeader() {
        return oneTokenConfig.getHeader();
    }
    protected Boolean getClientShare() {
        return oneTokenConfig.getClientShare();
    }


}

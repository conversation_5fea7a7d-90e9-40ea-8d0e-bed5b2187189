package com.sub.framework.jwt.exception;

public enum LoginTokenEnum {
    LOGIN_SUCCESS( "login.success"),                // 登录成功
    LOGOUT_LOGIN_SUCCESS("logout.login.success"),   //退出登录成功
    LOGIN_INFO_PASTDUE("login.info.pastdue"),       // 短token信息过期
    LOGIN_IN_REST_LOGIN("login.in.rest.login"),     // 账号在其他地方登录
    LOGIN_INFO_ERROR("login.info.error"),           // 登录信息错误
    LOGOUT_LOGIN_FAIL("logout.login.fail")          //退出登录失败
    ;

    private String message;

    LoginTokenEnum(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }
}

package com.sub.framework.jwt.constant;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "token 信息解析")
public class TokenResult implements Serializable {
    @Schema(description = "用户id")
    private String userId;
    @Schema(description = "主题")
    private String sub;
    @Schema(description = "长 token ")
    private String refreshToken;
    @Schema(description = "请求的客户端")
    private String client;
    @Schema(description = "开始时间")
    private Long iat;
    @Schema(description = "过期时间")
    private Long exp;
}

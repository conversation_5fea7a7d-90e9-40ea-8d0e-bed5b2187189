package com.sub.framework.jwt.constant;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Component
@ConfigurationProperties(prefix = "one-token")
public class OneTokenConfig {
    @Schema(description = "头部标识")
    private String header;
    @Schema(description = "加密的key")
    private String authKey;
    @Schema(description = "长token 时间类型")
    private String longType;
    @Schema(description = "长token 持续时间")
    private Integer longTimeout;
    @Schema(description = "长token 在那个有效时间内进行替换")
    private Integer replaceTimeout;
    @Schema(description = "是否支持同端互斥")
    private Boolean clientShare;
    @Schema(description = "短token时间类型")
    private String shortType;
    @Schema(description = "短token 持续时间")
    private Integer shortTimeout;
}

package com.sub.framework.config;

import java.util.Locale;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.web.servlet.LocaleResolver;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 国际化语言解析器
 * 支持中文、英文、日语、韩语四种语言
 * 前端传递的Accept-Language值为：zh、en、ja、ko
 */
public class I18nLocaleResolver implements LocaleResolver {

    private static final Logger log = LoggerFactory.getLogger(I18nLocaleResolver.class);

    // 默认语言
    private static final Locale DEFAULT_LOCALE = new Locale("zh", "CN");

    @Override
    @NonNull
    public Locale resolveLocale(@NonNull HttpServletRequest request) {
        String acceptLanguage = request.getHeader("Accept-Language");

        if (StringUtils.isEmpty(acceptLanguage)) {
            log.debug("Accept-Language header is empty, using default locale: {}", DEFAULT_LOCALE);
            return DEFAULT_LOCALE;
        }

        // 清理语言代码，移除空格并转为小写
        String language = acceptLanguage.trim().toLowerCase();

        log.debug("Resolving locale from Accept-Language: {}", language);

        // 直接匹配前端传递的四个固定值
        Locale resolvedLocale;
        switch (language) {
            case "zh":
                resolvedLocale = new Locale("zh", "CN");
                break;
            case "en":
                resolvedLocale = new Locale("en", "US");
                break;
            case "ja":
                resolvedLocale = new Locale("ja", "JP");
                break;
            case "ko":
                resolvedLocale = new Locale("ko", "KR");
                break;
            default:
                log.debug("Unsupported language: {}, using default locale", language);
                resolvedLocale = DEFAULT_LOCALE;
                break;
        }

        log.debug("Resolved locale: {}", resolvedLocale);
        return resolvedLocale;
    }

    @Override
    public void setLocale(@NonNull HttpServletRequest request, @Nullable HttpServletResponse response, @Nullable Locale locale) {
        // 此方法在当前实现中不需要具体逻辑
        // 语言解析完全基于请求头的Accept-Language
    }
}

## SubFeeds
**介绍:** 此项目是一个类似于咨询网站, 目的是让用户快速便捷的获取世界上最新发生的事情, 同时可避免各平台单一数据算法推荐, 从而打破
信息蚕房,达到为用户节省时间, 提高信息获取效率等. <br>


###技术采用
后台: 采用SpringBoot2.6, Java11, Mysql8.0+, Redis <br>
订阅分为: 
- 系统自建<br>
- 三方订阅源<br>



### API 接口简述
 仅本地开发时开放, 在线调试地址  http://192.168.31.189:8999/doc.html <br>
 本文档只做简述, 也就是接口调用前的前置条件, 具体接口参数请详细看:  http://192.168.31.189:8999/doc.html <br>
 注意部分接口需要进行鉴权操作, 请在请求头部添加以下参数 -H Authorization Bearer <token(String)>
#### 发送验证码
对于一些需要验证的操作时, 都可以调用此接口, 只需注意 channel 和 type 的参数即可, 以下接口调用前 必须调用此接口
 - 注册, 忘记密码, 验证账号
   
    






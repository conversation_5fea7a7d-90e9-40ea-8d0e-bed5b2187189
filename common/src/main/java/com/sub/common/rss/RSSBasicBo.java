package com.sub.common.rss;


import java.io.Serializable;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "RSS 基础类")
public class RSSBasicBo implements Serializable {
    private String title;

    private String description;

    private String link;

    private String guid;

    private Date pubDate;
}

package com.sub.common.exception;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(description = "统一异常处理!!")
public class BaseException extends Exception {
    @Schema(description = "错误码")
    private Integer code;
    @Schema(description = "错误参数")
    private String msg;

    /**
     * 构造函数：只传入错误信息，使用默认错误码-300
     */
    public BaseException(String msg) {
        super(msg); // 调用父类构造函数，设置Exception的message字段
        this.code = -300;
        this.msg = msg;
    }

    /**
     * 构造函数：传入错误码和错误信息
     */
    public BaseException(Integer code, String msg) {
        super(msg); // 调用父类构造函数
        this.code = code;
        this.msg = msg;
    }

    /**
     * 构造函数：传入错误信息和原因异常，使用默认错误码-300
     */
    public BaseException(String msg, Throwable cause) {
        super(msg, cause); // 调用父类构造函数，同时设置cause
        this.code = -300;
        this.msg = msg;
    }

    /**
     * 构造函数：传入错误码、错误信息和原因异常
     */
    public BaseException(Integer code, String msg, Throwable cause) {
        super(msg, cause); // 调用父类构造函数
        this.code = code;
        this.msg = msg;
    }

    /**
     * 重写getMessage方法，确保返回正确的错误信息
     * 优先返回自定义的msg字段，如果为空则返回父类的message
     */
    @Override
    public String getMessage() {
        return msg != null ? msg : super.getMessage();
    }
}

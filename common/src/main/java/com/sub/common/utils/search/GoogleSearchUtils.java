package com.sub.common.utils.search;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.sub.common.core.common.HttpResultBo;
import com.sub.common.core.common.SearchESPo;
import com.sub.common.utils.http.HttpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Component
public class GoogleSearchUtils {
    private Logger log = LoggerFactory.getLogger(GoogleSearchUtils.class);

    private final static String cx = "d1d383607a9a24a62";
    private final static String apiKey = "AIzaSyDWakCPGMmqxssCExrzLGlvucmzv4na-Fc";
    private final static String dateRestict = "w1";
    private final static String queryUrl = "https://www.googleapis.com/customsearch/v1?q=%s&cx=%s&key=%s&dateRestrict=%s";


    public List<SearchESPo> getSESearchDataList(String searchValue) throws Exception {
        String url = String.format(queryUrl,
                URLEncoder.encode(searchValue, StandardCharsets.UTF_8.toString()),
                cx, apiKey, dateRestict
                );

        String result = HttpUtils.sendGet(url).getData();
        JSONObject jsonObject = JSONObject.parseObject(result);
        JSONArray items = jsonObject.getJSONArray("items");
        List<SearchESPo> list = new ArrayList<>();
        if (items != null) {
            for (int i = 0; i < items.size(); i++) {
                if (i > 1) {
                    JSONObject item = items.getJSONObject(i);
                    SearchESPo  searchESPo = new SearchESPo();
                    searchESPo.setTitle(item.getString("title"));
                    searchESPo.setLink(item.getString("link"));
                    searchESPo.setDesc(item.getString("htmlSnippet"));
                    JSONObject pagemap = item.getJSONObject("pagemap");
                    if (pagemap != null) {
                        JSONArray imageArray = pagemap.getJSONArray("cse_thumbnail");
                        if (imageArray != null && imageArray.size() > 0) {
                            JSONObject image = (JSONObject) imageArray.get(0);
                            searchESPo.setImage(image.getString("src"));
                        }
                    }
                    list.add(searchESPo);
                }
            }
        }
        return list;
    }




}

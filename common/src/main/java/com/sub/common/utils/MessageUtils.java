package com.sub.common.utils;


import com.sub.common.utils.spring.SpringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

/**
 * 获取i18n资源文件
 */
public class MessageUtils {
    private static Logger log = LoggerFactory.getLogger(MessageUtils.class);

    /**
     * 根据消息键和参数 获取消息 委托给spring messageSource
     * @param code
     * @param args
     * @return
     */
    public static String message(String code, Object... args) {
        try {
            MessageSource messageSource = SpringUtils.getBean(MessageSource.class);
            return messageSource.getMessage(code, args, LocaleContextHolder.getLocale());
        } catch (Exception e) {
            log.error("I18文件中未找该code: {}, 具体错误如下: {}", code, e.getMessage());
            return code;
        }
    }

    public static String getRequestLanguage() {
        return LocaleContextHolder.getLocale().getLanguage();
    }
}

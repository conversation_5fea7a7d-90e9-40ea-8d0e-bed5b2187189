package com.sub.common.utils.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sub.common.utils.StringUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

public class BigDecimalSerializer extends JsonSerializer<BigDecimal> {
    private final static DecimalFormat df = new DecimalFormat("###,##0.00");

    @Override
    public void serialize(BigDecimal bigDecimal, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (StringUtils.isNull(bigDecimal)) {
            bigDecimal = new BigDecimal("0.00");
        }
        jsonGenerator.writeString(df.format(bigDecimal.setScale(2, RoundingMode.HALF_UP)));
    }
}

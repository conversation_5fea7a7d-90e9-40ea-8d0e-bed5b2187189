package com.sub.common.utils;

import com.sub.common.enums.TimeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.sql.Time;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.IsoFields;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;
import java.util.Date;

/**
 * 时间戳
 */
@Component
public class TimeUtils {
    private Logger log = LoggerFactory.getLogger(TimeUtils.class);
    /**
     * 格式化日期数组
     */
    private final static SimpleDateFormat[] FORMATS = new SimpleDateFormat[] {
            new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"),
            new SimpleDateFormat("yyyy-MM-dd HH:mm"),
            new SimpleDateFormat("yyyy-MM-dd HH"),
            new SimpleDateFormat("yyyy-MM-dd"),
            new SimpleDateFormat("yyyy-MM"),
            new SimpleDateFormat("yyyy"),
            new SimpleDateFormat("yyyyMMdd")
    };
    private final static SimpleDateFormat[] PARAM_FORMATS = new SimpleDateFormat[] {
            new SimpleDateFormat("yyyyMMddHHmmss")
    };

    /** 获取当前时间戳 **/
    public static long getCurrentTime() {
        return System.currentTimeMillis() / 1000;
    }

    /** 获取现在时间 yyyy-MM-dd HH:mm:ss **/
    public static String getCurrentData() {
        Date date = new Date();
        return FORMATS[3].format(date);
    }

    public static String getCurrentData(Integer index) {
        Date date = new Date();
        return FORMATS[index].format(date);
    }

    /**
     * 字符转日期
     * 
     * @param dateString
     * @param index
     * @return
     */
    public static Date getTimeCaseDate(String dateString, int index) {
        try {
            return FORMATS[index].parse(dateString);
        } catch (ParseException e) {
            return new Date();
        }
    }

    /**
     * 时间戳转日期
     */
    public static String timestampCauseDateStr(Long time, int index) {
        Date date = new Date(time);
        return FORMATS[index].format(date);
    }

    /**
     * 日期转 字符
     * 
     * @param data
     */
    public static String dateConvertFormStr(Date data) {
        return FORMATS[0].format(data);
    }

    /** 根据TimeEnum 计算返回多少秒的 **/
    public static long getTimeEnumSecond(Integer num, TimeEnum timeEnum) {
        long compute = 0;
        switch (timeEnum) {
            case WEEK:
                // 计算出(num) 周 以后的时间搓
                compute = 60 * 60 * 24 * 7 * num;
                break;
            case DAY:
                // 计算出(num) 天 以后的时间搓
                compute = 60 * 60 * 24 * num;
                break;
            case HOUR:
                // 计算出(num) 小时 以后的时间搓
                compute = 60 * 60 * num;
                break;
            case MINUTE:
                // 计算出(num) 分钟 以后的时间搓
                compute = 60 * num;
                break;
            case SECOND:
                // 计算出(num) 秒 以后的 时间搓
                compute = num;
                break;
        }
        return compute;
    }

    public static long getComputerStamp(Integer num, long timestamp, TimeEnum timeEnum) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(timestamp * 1000);
        switch (timeEnum) {
            case YEAR:
                // 计算出(num) 年 以后的时间搓
                calendar.add(Calendar.YEAR, num);
                break;
            case MONTH:
                // 计算出(num) 月 以后的时间搓
                calendar.add(Calendar.MONTH, num);
                break;
            case WEEK:
                // 计算出(num) 周 以后的时间搓
                calendar.add(Calendar.WEDNESDAY, num);
                break;
            case DAY:
                // 计算出(num) 天 以后的时间搓
                calendar.add(Calendar.DATE, num);
                break;
            case HOUR:
                // 计算出(num) 小时 以后的时间搓
                calendar.add(Calendar.HOUR, num);
                break;
            case MINUTE:
                // 计算出(num) 分钟 以后的时间搓
                calendar.add(Calendar.MINUTE, num);
                break;
        }
        return calendar.getTimeInMillis() / 1000;
    }

    /**
     * 日期增加时间戳
     * 
     * @param date
     * @param timeStamp
     * @return
     */
    public static long getAddTimeStamp(Date date, Long timeStamp) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(date.getTime());
        calendar.add(Calendar.SECOND, timeStamp.intValue());
        return calendar.getTimeInMillis();
    }

    /**
     * 两个时间戳相隔 多少, 以可读方式返回: 如: 36分钟
     */
    public static String getTimeBetween(long time1, long time2) {
        Instant instant1 = Instant.ofEpochSecond(time1);
        Instant instant2 = Instant.ofEpochSecond(time2);

        Duration duration = Duration.between(instant1, instant2);
        long totalMinutes = duration.toMinutes();
        long days = totalMinutes / (60 * 24);
        long hours = (totalMinutes % (60 * 24)) / 60;
        long minutes = totalMinutes % 60;
        StringBuilder sb = new StringBuilder();
        if (days > 0) {
            sb.append(days).append("天");
        }
        if (hours > 0) {
            sb.append(hours).append("小时");
        }
        if (minutes > 0) {
            sb.append(minutes).append("分钟");
        }
        return sb.toString();
    }

    /**
     * 生成表名后缀
     * 
     * @param isNext 是否生成下一期, false 本周 true 下周
     */
    public static String generateTableSuffixName(boolean isNext) {
        return generateTableSuffixName(getCurrentTime(), isNext);
    }

    public static String generateTableSuffixName(Long createTime, boolean isNext) {
        // 将时间戳转换为 LocalDate
        Instant instant = Instant.ofEpochSecond(createTime);
        LocalDate now = instant.atZone(java.time.ZoneId.systemDefault()).toLocalDate();
        int year = now.get(IsoFields.WEEK_BASED_YEAR) % 100;
        int week = now.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
        if (isNext) {
            // 获取下周的日期
            LocalDate nextWeekDate = now.plusWeeks(1);
            year = nextWeekDate.get(IsoFields.WEEK_BASED_YEAR) % 100;
            week = nextWeekDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
        }
        return String.format("_%02d_%02d", year, week);
    }

    public static String generateTableSuffixName(Integer index) {
        Instant instant = Instant.ofEpochSecond(getCurrentTime());
        LocalDate now = instant.atZone(java.time.ZoneId.systemDefault()).toLocalDate();
        // 根据index 计算距离多少周
        LocalDate nextWeekDate = now.plusWeeks(index);
        int year = nextWeekDate.get(IsoFields.WEEK_BASED_YEAR) % 100;
        int week = nextWeekDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
        return String.format("_%02d_%02d", year, week);
    }

    public static Date dateTimeConversion(String dateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;
        LocalDateTime localDateTime = LocalDateTime.parse(dateTime, formatter);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取本月月初时间戳
     */
    public static long getMonthStartTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis() / 1000;
    }

    /**
     * 获取下个月1号0点时间戳（用作月末时间戳）
     */
    public static long getMonthEndTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.add(Calendar.MONTH, 1);
        return calendar.getTimeInMillis() / 1000;
    }

    /**
     * 获取本年起始时间戳
     */
    public static long getYearStartTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis() / 1000;
    }

    /**
     * 获取下一年1月1号0点时间戳（用作年末时间戳）
     */
    public static long getYearEndTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.add(Calendar.YEAR, 1);
        return calendar.getTimeInMillis() / 1000;
    }

}

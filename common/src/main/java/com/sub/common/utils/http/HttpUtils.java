package com.sub.common.utils.http;


import com.alibaba.fastjson2.JSONObject;

import com.sub.common.constant.Constants;
import com.sub.common.core.common.HttpResultBo;
import com.sub.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 通用http发送方法
 */
public class HttpUtils {
    private static final Logger log = LoggerFactory.getLogger(HttpUtils.class);

    /**
     * 向指定 URL 发送GET方法的请求
     * @param url 发送请求的 URL
     * @return 所代表远程资源的响应结果
     */
    public static HttpResultBo sendGet(String url) {
        return sendGet(url, null, 10000);
    }

    public static HttpResultBo sendGet(String url, String charset) {
        if (StringUtils.isEmpty(charset)) {
            charset = Constants.UTF8;
        }
        return sendGet(url, null, charset , 10000);
    }

    public static HttpResultBo sendGet(String url, int readTime) {
        return sendGet(url, null, readTime);
    }

    /**
     * 向指定 URL 发送GET方法的请求
     * @param url   发送请求的 URL
     * @param headerMap 请求头部数据
     * @return 所代表远程资源的响应结果
     */
    public static HttpResultBo sendGet(String url,  Map<String, String> headerMap, int readTime) {
        return sendGet(url, headerMap, Constants.UTF8, readTime);
    }

    /**
     * 向指定 URL 发送GET方法的请求
     * @param url         发送请求的 URL
     * @param headerMap   请求头部数据
     * @param charset     编码类型
     * @return 所代表远程资源的响应结果
     */
    public static HttpResultBo sendGet(String url, Map<String, String> headerMap, String charset, int readTime){
        if (StringUtils.isEmpty(charset)) {
            charset = Constants.UTF8;
        }
        StringBuilder result = new StringBuilder();
        Integer status = 0;
        String message = null;
        Map<String, List<String>> headers = new HashMap<>();
        try {
            log.info("sendGet - {}", url);
            URL realUrl = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) realUrl.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(readTime);
            connection.setReadTimeout(readTime);
            if (headerMap != null && !headerMap.isEmpty()) {
                for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                    connection.setRequestProperty(entry.getKey(), entry.getValue());
                }
            }
            connection.connect();
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK || responseCode == HttpURLConnection.HTTP_CREATED) {
                status = responseCode;
                readResponse(connection, result, charset);
                headers = connection.getHeaderFields();
            } else if (responseCode == HttpURLConnection.HTTP_MOVED_PERM || responseCode == HttpURLConnection.HTTP_MOVED_TEMP) {
                String location = connection.getHeaderField("Location");
                log.info("Redirected To: " + location);
                URL redirectedUrl = new URL(location);
                HttpURLConnection redirectedConnection = (HttpURLConnection) redirectedUrl.openConnection();
                redirectedConnection.setRequestMethod("GET");
                redirectedConnection.setConnectTimeout(readTime);
                redirectedConnection.setReadTimeout(readTime);
                if (headerMap != null && !headerMap.isEmpty()) {
                    for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                        redirectedConnection.setRequestProperty(entry.getKey(), entry.getValue());
                    }
                }
                redirectedConnection.connect();
                int redirectedResponseCode = redirectedConnection.getResponseCode();
                if (redirectedResponseCode == HttpURLConnection.HTTP_OK) {
                    readResponse(redirectedConnection, result, charset);
                    headers = connection.getHeaderFields();
                }
            }
        }catch (MalformedURLException e) {
            message = "Malformed URL: " + url;
            log.error("Malformed URL: {}, error: {}", url, e.getMessage());
        } catch (IOException e) {
            message = "IO Exception: " + e.getMessage();
            log.error("IO Exception for URL: {}, error: {}", url, e.getMessage());
        } catch (Exception e) {
            message = "General Exception: " + e.getMessage();
            log.error("General Exception for URL: {}, error: {}", url, e.getMessage());
        }
        return new HttpResultBo(status, message, result.toString(), url, headers);
    }
    private static void readResponse(HttpURLConnection connection, StringBuilder result, String charset) throws IOException {
        try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), charset))) {
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
        }
    }

    public static String sendPost(String url) {
        return sendPost(url, null, null);
    }
    public static String sendPost(String url, JSONObject jsonObject) {
        return sendPost(url, jsonObject, null);
    }

    /**
     * 向指定 URL 发送POST方法的请求
     * @param url   发送请求的 URL
     * @param jsonParam 请求参数
     * @param headerMap 请求头部
     * @return 所代表远程资源的响应结果
     */
    public static String sendPost(String url, JSONObject jsonParam, Map<String,String> headerMap) {
        PrintWriter out = null;
        BufferedReader in = null;
        StringBuilder result = new StringBuilder();
        try {
            log.info("sendPost - {}", url);
            URL realUrl = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) realUrl.openConnection();
            connection.setRequestMethod("POST");
            connection.setConnectTimeout(60000);
            connection.setReadTimeout(60000);
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)");
            connection.setRequestProperty("Accept-Charset", "utf-8");
            connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
            if (headerMap != null && headerMap.size() > 0) {
                Set<String> keySet = headerMap.keySet();
                for (String key : keySet) {
                    connection.setRequestProperty(key, headerMap.get(key));
                }
            }
            connection.setDoOutput(true);
            connection.setDoInput(true);
            out = new PrintWriter(connection.getOutputStream());
            if (jsonParam != null) {
                out.print(jsonParam.toString());
            }
            out.flush();
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK || responseCode == HttpURLConnection.HTTP_CREATED) {
                in = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
                String line;
                while ((line = in.readLine()) != null) {
                    result.append(line);
                }
            } else {
                log.warn("调用HttpUtils.sendPost status exception, url=" + url + responseCode);
            }
        } catch (ConnectException e) {
            log.error("调用HttpUtils.sendPost ConnectException, url=" + url + ",param=" + jsonParam, e);
        } catch (SocketTimeoutException e) {
            log.error("调用HttpUtils.sendPost SocketTimeoutException, url=" + url + ",param=" + jsonParam, e);
        } catch (IOException e) {
            log.error("调用HttpUtils.sendPost IOException, url=" + url + ",param=" + jsonParam, e);
        } catch (Exception e) {
            log.error("调用HttpsUtil.sendPost Exception, url=" + url + ",param=" + jsonParam, e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                log.error("调用in.close Exception, url=" + url + ",param=" + jsonParam, ex);
            }
        }
        return result.toString();
    }

    public static String sendSSLPost(String url, String param) {
        StringBuilder result = new StringBuilder();
        String urlNameString = url + "?" + param;
        try {
            log.info("sendSSLPost - {}", urlNameString);
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, new TrustManager[]{new TrustAnyTrustManager()}, new java.security.SecureRandom());
            URL console = new URL(urlNameString);
            HttpsURLConnection conn = (HttpsURLConnection) console.openConnection();
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)");
            conn.setRequestProperty("Accept-Charset", "utf-8");
            conn.setRequestProperty("contentType", "utf-8");
            conn.setDoOutput(true);
            conn.setDoInput(true);

            conn.setSSLSocketFactory(sc.getSocketFactory());
            conn.setHostnameVerifier(new TrustAnyHostnameVerifier());
            conn.connect();
            InputStream is = conn.getInputStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(is));
            String ret = "";
            while ((ret = br.readLine()) != null) {
                if (ret != null && !"".equals(ret.trim())) {
                    result.append(new String(ret.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8));
                }
            }
            log.info("recv - {}", result);
            conn.disconnect();
            br.close();
        } catch (ConnectException e) {
            log.error("调用HttpUtils.sendSSLPost ConnectException, url=" + url + ",param=" + param, e);
        } catch (SocketTimeoutException e) {
            log.error("调用HttpUtils.sendSSLPost SocketTimeoutException, url=" + url + ",param=" + param, e);
        } catch (IOException e) {
            log.error("调用HttpUtils.sendSSLPost IOException, url=" + url + ",param=" + param, e);
        } catch (Exception e) {
            log.error("调用HttpsUtil.sendSSLPost Exception, url=" + url + ",param=" + param, e);
        }
        return result.toString();
    }

    private static class TrustAnyTrustManager implements X509TrustManager {
        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) {
        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) {
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[]{};
        }
    }

    private static class TrustAnyHostnameVerifier implements HostnameVerifier {
        @Override
        public boolean verify(String hostname, SSLSession session) {
            return true;
        }
    }


    /**
     * 链接测速
     */
    public static long openUrlSpeed(String urlStr) {
        try {
            long startTime;
            URL url = new URL(urlStr);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD"); // 使用HEAD方法，只获取响应头，不获取响应体
            connection.setConnectTimeout(30000); // 设置连接超时（毫秒）
            connection.setReadTimeout(30000);     // 设置读取超时（毫秒）
            startTime = System.currentTimeMillis();
            connection.connect();;
            return System.currentTimeMillis() - startTime;
        } catch (Exception e) {
            return -1L;
        }
    }


}

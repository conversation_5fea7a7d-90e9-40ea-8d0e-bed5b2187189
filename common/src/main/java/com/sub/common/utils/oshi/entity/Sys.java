package com.sub.common.utils.oshi.entity;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 系统相关信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "系统相关信息")
public class Sys implements Serializable {
    @Schema(description = "服务器名称")
    private String computerName;
    @Schema(description = "服务器ip")
    private String computerIp;
    @Schema(description = "项目路径")
    private String userDir;
    @Schema(description = "操作系统")
    private String osName;
    @Schema(description = "系统架构")
    private String osArch;
}

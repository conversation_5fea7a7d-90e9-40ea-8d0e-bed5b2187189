package com.sub.common.utils.ip;


import com.sub.common.utils.StringUtils;
import org.lionsoul.ip2region.xdb.Searcher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;

public class IP2RegionUtil {
    private static final Logger log = LoggerFactory.getLogger(IP2RegionUtil.class);

    public static Searcher SEARCHER;

    static {
        ClassPathResource resource = new ClassPathResource(
                "ip2region.xdb"
        );
        try {
            byte[] ip_db = Searcher.loadContentFromFile(resource.getFile().getAbsolutePath());
            SEARCHER = Searcher.newWithBuffer(ip_db);
        } catch (IOException e) {
            log.error("加载ip2location bin文件失败");
        }
    }

    public static String getAppointIpRegion(String ip) {
        String region = null;
        try {
            region = SEARCHER.search(ip);
        } catch (Exception e) {
            log.warn("ip 地址解析失败");
            return "未知IP";
        }
        if (StringUtils.isEmpty(region)) {
            return "未知IP";
        }
        Integer lastIndex = region.lastIndexOf('|');
        if (lastIndex != -1) {
            String withoutLastPipe = region.substring(0, lastIndex).replace("|0|", "-");
            region = withoutLastPipe.replace("|", "-");
        }
        return region;
    }
}

package com.sub.common.utils.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.sub.common.utils.StringUtils;
import com.sub.common.utils.auth.CompressionUtil;

import java.io.IOException;
import java.util.Base64;

/**
 * Description 对象的序列化器
 */
public class DescriptionSerializer extends JsonSerializer<String> {

    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (StringUtils.isEmpty(value) ) {
            gen.writeNull();
            return;
        }
        if (!value.startsWith("H4sIAAAAAAAA")) {
            gen.writeString(value);
        } else {
            byte[] compressedData = Base64.getDecoder().decode(value.getBytes());
            if (compressedData != null && compressedData.length > 0) {
                String decompressedContent = CompressionUtil.decompress(compressedData);
                gen.writeString(decompressedContent);
            }
        }
    }
}

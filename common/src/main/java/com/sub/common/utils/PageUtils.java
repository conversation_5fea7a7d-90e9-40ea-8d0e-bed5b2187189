package com.sub.common.utils;

import com.github.pagehelper.PageHelper;
import com.sub.common.core.page.PageBo;
import com.sub.common.core.page.TableSupportBo;
import com.sub.common.utils.sql.SqlUtil;


/**
 * 分页工具类
 */
public class PageUtils extends PageHelper {

    /**
     * 设置请求分页数据
     */
    public static void startPage() {
        PageBo pageBo = TableSupportBo.setPageBo();
        Integer pageNum = pageBo.getPageNum();
        Integer pageSize = pageBo.getPageSize();
        String orderBy = SqlUtil.escapeOrderBySql(pageBo.getOrderBy());
        Boolean reasonable = pageBo.getReasonable();
        PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(reasonable);
    }

    public static String startNoOrderPage() {
        PageBo pageBo = TableSupportBo.setPageBo();
        Integer pageNum = pageBo.getPageNum();
        Integer pageSize = pageBo.getPageSize();
        Boolean reasonable = pageBo.getReasonable();
        PageHelper.startPage(pageNum, pageSize).setReasonable(reasonable);
        return pageBo.getOrderBy();
    }



    /**
     * 设置随机 请求分页数据
     */
    public static void randomStartPage(String column) {
        PageBo pageBo = TableSupportBo.setRecommendPageBo(column);
        Integer pageNum = pageBo.getPageNum();
        Integer pageSize = pageBo.getPageSize();
        String orderBy = SqlUtil.escapeOrderBySql(pageBo.getOrderBy());
        Boolean reasonable = pageBo.getReasonable();
        PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(reasonable);
    }


    /**
     * 清理分页的线程变量
     */
    public static void clearPage() {
        PageHelper.clearPage();
    }
}

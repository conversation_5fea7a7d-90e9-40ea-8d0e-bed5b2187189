package com.sub.common.utils.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.github.pagehelper.util.StringUtil;

import java.io.IOException;

/**
 * 敏感数据  设置全部
 */
public class IsSettingSerializer  extends JsonSerializer<String> {
    @Override
    public void serialize(String s, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (StringUtil.isNotEmpty(s)) {
            jsonGenerator.writeObject("******");
        } else {
            jsonGenerator.writeObject(s);
        }
    }
}

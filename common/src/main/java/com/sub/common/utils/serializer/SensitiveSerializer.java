package com.sub.common.utils.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.github.pagehelper.util.StringUtil;
import com.sub.common.utils.StringUtils;

import java.io.IOException;

/**
 * 敏感数据  设置部分
 */
public class SensitiveSerializer extends JsonSerializer<String> {

    @Override
    public void serialize(String s, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (!StringUtil.isEmpty(s)) {
            if(StringUtils.isEmail(s)) {
                String[] strSplitArray = s.split("@");
                s = strSplitArray[0].replaceAll("(?<=^.{3}).*(?=.{3}$)", "****")
                        + "@" +  strSplitArray[strSplitArray.length - 1];
            } else {
                s = s.replaceAll("(?<=^.{3}).*(?=.{4}$)", "****");
            }
        }
        jsonGenerator.writeObject(s);
    }
}

package com.sub.common.utils.oshi.entity;

import java.io.Serializable;

import com.sub.common.utils.Arith;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 内存相关信息
 */
@Schema(description = "内存相关信息")
public class Mem implements Serializable {
    @Schema(description = "内存总容量")
    private double total;
    @Schema(description = "已用内存")
    private double used;
    @Schema(description = "剩余内存")
    private double free;

    public double getTotal()
    {
        return Arith.div(total, (1024 * 1024 * 1024), 2);
    }

    public void setTotal(long total)
    {
        this.total = total;
    }

    public double getUsed()
    {
        return Arith.div(used, (1024 * 1024 * 1024), 2);
    }

    public void setUsed(long used)
    {
        this.used = used;
    }

    public double getFree()
    {
        return Arith.div(free, (1024 * 1024 * 1024), 2);
    }

    public void setFree(long free)
    {
        this.free = free;
    }

    public double getUsage()
    {
        return Arith.mul(Arith.div(used, total, 4), 100);
    }
}

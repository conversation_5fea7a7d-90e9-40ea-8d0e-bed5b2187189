package com.sub.common.utils.http;

import com.sub.common.utils.StringUtils;
import com.sub.common.utils.auth.AuthUtils;

import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLDecoder;

public class URLUtils {


    /**
     * 从完整的 URL 中提取协议和主机名
     */
    public static String getSchemeAndHost(String fullUrl) {
        try {
            URL url = new URL(fullUrl);
            return url.getProtocol() + "://" + url.getHost() + (url.getPort() != -1 ? ":" + url.getPort() : "");
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 提取主机
     */
    public static String getHost(String fullUrl) {
        try {
            URL url = new URL(fullUrl);
            return url.getHost();
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * 从完整的 URL 中提取顶级域名
     */
    public static String getTopLevelDomain(String fullUrl) {
        try {
            URL url = new URL(fullUrl);
            String host = url.getHost();
            // 使用正则表达式匹配点字符，包括全角点字符
            String[] parts = host.split("[\\p{Punct}\\p{S}\\u22C5]");
            if (parts.length < 2) {
                return url.getProtocol() + "://" + host;
            }
            // 处理特殊情况，确保返回正确的顶级域名
            StringBuilder topLevelDomain = new StringBuilder();
            for (int i = parts.length - 2; i < parts.length; i++) {
                if (topLevelDomain.length() > 0) {
                    topLevelDomain.append(".");
                }
                topLevelDomain.append(parts[i]);
            }
            return url.getProtocol() + "://" + topLevelDomain.toString();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * url md5 加权
     */
    public static String urlMd5Weight(String url) {
        if (StringUtils.isNotEmpty(url) && url.endsWith("/")) {
            url = url.substring(0, url.length() - 1);
        }
        return AuthUtils.md5Hex(url.replaceAll("https", "http").replaceAll("www.", ""));
    }

    /**
     * url 参数去除
     */
    public static String urlReplace(String url) {
        if (StringUtils.isNotEmpty(url) && url.endsWith("/")) {
            url = url.substring(0, url.length() - 1);
        }
        return url.replaceAll("https", "http").replaceAll("www.", "");
    }

    /**
     * url 去掉 / 后面的参数
     */
    public static String urlLastReplace(String url) {
        if (StringUtils.isNotEmpty(url) && url.endsWith("/")) {
            url = url.substring(0, url.length() - 1);
        }
        return url;
    }


    /**
     * url 去掉 ? 后面的参数
     * @param url
     * @return
     */
    public static String removeQueryParameters(String url) {
        if (StringUtils.isEmpty(url)) {
            return url; // 如果输入为空，直接返回
        }
        // 找到第一个 "?" 的位置
        int queryIndex = url.indexOf('?');
        if (queryIndex != -1) {
            // 截取 "?" 之前的部分
            url = url.substring(0, queryIndex);
        }
        return url;
    }

    /**
     * 获取第一个查询参数
     * @param url
     * @return
     */
    public static String getFristQueryParameters(String url) {
        int queryStartIndex = url.indexOf('?');
        if (queryStartIndex == -1) {
            return null; // 没有查询字符串
        }
        String query = url.substring(queryStartIndex + 1);
        String[] params = query.split("&");
        if (params.length == 0) {
            return query;
        }
        String[] pair = params[0].split("=");
        if (pair.length < 2) {
            return null; // 参数格式不正确
        }
        // 解码参数值
        try {
            return URLDecoder.decode(pair[1], "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取最后一个路径段
     * @param url
     * @return
     */
    public static String getLastPathSegment(String url) {
        if (url == null || url.isEmpty()) {
            return null;
        }
        // 去掉URL末尾的斜杠
        if (url.endsWith("/")) {
            url = url.substring(0, url.length() - 1);
        }
        // 找到最后一个斜杠的位置
        int lastSlashIndex = url.lastIndexOf('/');
        if (lastSlashIndex == -1) {
            return null; // 没有路径段
        }
        return url.substring(lastSlashIndex + 1);
    }


    /**
     * 验证链接是否含有多个 https, 如果有则获取第一个
     */
    public static String extractFirstLink(String input) {
        if (StringUtils.isEmpty(input)) {
            return null;
        }
        String[] links = input.split("(?=(https?://))");
        if (links.length > 0) {
            return links[0];
        } else {
            return input;
        }
    }

}

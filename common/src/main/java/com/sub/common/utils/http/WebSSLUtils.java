package com.sub.common.utils.http;

import java.net.URL;
import java.security.cert.X509Certificate;

import javax.net.ssl.HttpsURLConnection;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sub.common.core.common.WebSSLPo;
import com.sub.common.exception.BaseException;
import com.sub.common.utils.TimeUtils;

/**
 * web 站点证书检测
 */
public class WebSSLUtils {
    private static Logger log = LoggerFactory.getLogger(WebSSLUtils.class);


    /**
     *  获取站点 SSL证书信息
     * @param urlStr
     * @return
     * @throws Exception
     */
    public static WebSSLPo getWebSSLDetails(String urlStr) throws Exception {
        URL url = new URL(urlStr);
        HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
        connection.connect();

        X509Certificate[] peerCertificates = (X509Certificate[]) connection.getServerCertificates();
        if (peerCertificates.length > 0) {
            X509Certificate certificate = (X509Certificate) peerCertificates[0];
            WebSSLPo webSSLPo = new WebSSLPo();
            webSSLPo.setNoAfter(certificate.getNotAfter().getTime() / 1000);           // 开始时间
            webSSLPo.setNotBefore(certificate.getNotBefore().getTime() / 1000);        // 结束时间
            webSSLPo.setSigAlgName(certificate.getSigAlgName());                       // 签名算法
            webSSLPo.setIssuer(certificate.getIssuerX500Principal().getName());         // 证书提供者
            webSSLPo.setDayRemaining(( TimeUtils.getCurrentTime() - webSSLPo.getNoAfter()) / (60 * 60 * 24)); // 剩余天数
            return webSSLPo;
        }
        throw new Exception();
    }

    /**
     * 检查证书是否过期
     * @param urlStr
     * @return
     * @throws BaseException
     */
    public static boolean checkWebSSLExpiration(String urlStr) throws BaseException {
        try {
            WebSSLPo webSSLPo = getWebSSLDetails(urlStr);
            if (webSSLPo.getDayRemaining() >= 0) {
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("证书检测 失败!!!! 具体原因如下 " + e);
            throw new BaseException();
        }

    }

}

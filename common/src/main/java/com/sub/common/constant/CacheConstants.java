package com.sub.common.constant;

/**
 * 缓存的key 常量
 */
public class CacheConstants {
    /**
     * 系统规则
     */
    public static final String SETTING_KEY = "subm:setting:rule";
    /**
     * 验证码
     */
    public static final String VERIFY = "subm:verify:";

    /**
     * 登录账户密码错误次数
     */
    public static final String PWD_ERROR_COUNT = "subm:pwd:err:";

    /**
     * 系统token 前缀
     */
    public static final String TOKEN_LOGIN = "subm:login:token:";

    /**
     * 通知警告
     */
    public static final String NOTIFY_MAP = "subm:notify";


    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "subm:captcha:codes:";

    /**
     * 种类 redis key
     */
    public static final String subm_CATEGORY_KEY = "subm:category";

    /**
     * 文章 id redis key
     */
    public static final String subm_ARTICLE_ID_KEY = "subm:article:id";

    /**
     * 项目初始化数据缓存 redis key
     */
    public static final String PROJECT_INIT_KEY = "subm:project:init";
    public static final String PROJECT_PLAN_KEY = "subm:project:plan";

    /**
     * 提醒列表缓存 redis key
     */
    public static final String REMIND_LIST_KEY = "subm:remind:list";

    /**
     * 提醒统计缓存 redis key
     */
    public static final String REMIND_STAT_KEY = "subm:remind:stat";
}

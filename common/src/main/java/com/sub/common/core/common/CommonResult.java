package com.sub.common.core.common;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.github.pagehelper.PageInfo;
import com.sub.common.core.page.PageVo;
import com.sub.common.utils.MessageUtils;
import com.sub.common.utils.PageUtils;
import com.sub.common.utils.TimeUtils;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "公共返回结果信息")
public class CommonResult<T> implements Serializable {
    private static Logger log = LoggerFactory.getLogger(CommonResult.class);

    @Schema(description = "接口操作返回状态码, 0 以上表示成功 0以下表示失败", requiredMode = Schema.RequiredMode.REQUIRED)
    private int code;
    @Schema(description = "接口返回的描述信息, 有可能不返回", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String msg;
    @Schema(description = "新token 如果该值存在, 则需替换旧token", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String token;
    @Schema(description = "接口操作返回值封装的数据", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private T data;
    @Schema(description = "服务器时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long timestamp;

    protected void startPage() {
        PageUtils.startPage();
    }
    protected String startNoOrderPage() {
        return PageUtils.startNoOrderPage();
    }


    protected void recommendPage(String column) {
        PageUtils.randomStartPage(column);
    }


    protected void clearPage() {
        PageUtils.clearPage();
    }
    protected CommonResult getDataList(List<?> list) {
        PageVo rspData = new PageVo();
        PageInfo pageInfo = new PageInfo(list);
        rspData.setTotal(pageInfo.getTotal());
        rspData.setPageList(list);
        rspData.setPageSize(pageInfo.getPageSize());
        rspData.setPageNum(pageInfo.getPageNum());
        rspData.setSumPage(pageInfo.getPages());
        return ok(rspData);
    }

    protected CommonResult oneList(List<?> list) {
        PageVo rspData = new PageVo();
        rspData.setPageList(list);
        rspData.setTotal(1);
        rspData.setSumPage(1);
        rspData.setPageNum(1);
        return ok(rspData);
    }



    public CommonResult(int code) {
        this.code = code;
        this.timestamp = TimeUtils.getCurrentTime();
    }
    public CommonResult(T data) {
        this.code = 200;
        this.data = data;
        this.timestamp = TimeUtils.getCurrentTime();
    }
    public CommonResult(int code, T data) {
        this.code = code;
        this.data = data;
        this.timestamp = TimeUtils.getCurrentTime();
    }
    public CommonResult(int code, String token, T data) {
        this.code = code;
        this.token = token;
        this.data = data;
        this.timestamp = TimeUtils.getCurrentTime();
    }
    public CommonResult(int code, String msg) {
        this.code = code;
        this.msg = msg;
        this.timestamp = TimeUtils.getCurrentTime();
    }


    public static CommonResult ok(){return new CommonResult(200, MessageUtils.message("handler.success"));}
    public static <T> CommonResult<T> ok(T data) {
        return new CommonResult(data);
    }
    public static <T> CommonResult<T> ok(String message, T data) {
        return new CommonResult(200, message, data);
    }
    public static <T> CommonResult<T> ok(int code, String message) {return new CommonResult(code, message);}
    public static <T> CommonResult<T> ok(int code, T data) {
        return new CommonResult(code, data);
    }
    public static <T> CommonResult<T> ok(int code, String token, T data) {
        return new CommonResult(code, token, data);
    }
    public static <T> CommonResult<T> okToken(String token) {
        CommonResult commonResult = new CommonResult(100, MessageUtils.message("user.login.success"));
        commonResult.setToken(token);
        return commonResult;
    }

    public static <T> CommonResult<T> okay(String success) {
        Map<String, Object> map = new HashMap<>();
        map.put("content", success);
        return new CommonResult(map);
    }



    public static  <T> CommonResult<T> error(Integer code, String errorMessage) {
        return new CommonResult(code, errorMessage);
    }
    public static <T> CommonResult<T> error(Integer code, String errorMessage, T data) {
        return new CommonResult(code, errorMessage, data);
    }
    public static <T> CommonResult<T> error(String errorMessage) {
        return new CommonResult(-300, errorMessage);
    }
}

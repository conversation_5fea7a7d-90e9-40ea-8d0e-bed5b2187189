package com.sub.common.core.page;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.github.pagehelper.PageInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "分页数据返回")
public class PageVo<T> implements Serializable {
    @Schema(description = "总记录数")
    private long total;
    @Schema(description = "列表数据")
    private List<T> pageList;
    @Schema(description = "当前页")
    private long pageNum;
    @Schema(description = "每页记录数")
    private long pageSize;
    @Schema(description = "总共页数")
    private long sumPage;
    @Schema(description = "其他参数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Object other;

    /**
     * 分页
     * @param list  列表数据
     * @param total 总记录数
     */
    public PageVo(List<T> list, int total) {
        this.pageList = list;
        this.total = total;
    }

    public PageVo(List<T> list) {
        PageInfo pageInfo = new PageInfo(list);
        this.total = pageInfo.getTotal();
        this.pageSize = pageInfo.getPageSize();
        this.pageNum = pageInfo.getPageNum();
        this.sumPage = pageInfo.getPages();
    }

}

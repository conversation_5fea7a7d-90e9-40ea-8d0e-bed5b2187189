package com.sub.common.core.common;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "数据基类")
public class BaseEntity implements Serializable {
    @Schema(description = "唯一编号id")
    private String id;
    @Schema(description = "名称")
    @Size(min = 0, max = 50, message = "菜单名称长度不能超过50个字符")
    private String name;

    @Schema(description = "是否删除[0: 已删除 1: 正常]")
    private Integer enable;
    @Schema(description = "创建者")
    private String creator;
    @Schema(description = "创建时间")
    private Long createTime;
    @Schema(description = "更新者")
    private String updator;
    @Schema(description = "更新时间")
    private Long updateTime;

    @Schema(description = "开始时间")
    @JsonIgnore
    private Long startTime;
    @Schema(description = "最后操作时间")
    @JsonIgnore
    private Long endTime;

    @Schema(description = "更新操作说明")
    private String remark;

    @Schema(description = "公共筛选 字段, 单筛选框, 多条件筛选")
    @JsonIgnore
    public String search;
    @Schema(description = "特殊sql 判断语句, 可进行对相同mybatis sql 块执行不同sql 语句,  此参数前端不用管")
    @JsonIgnore
    private Map<String, Object> params;
    @Schema(description = "是否为 客户端 还是 后台, 此参数前端不用管")
    @JsonIgnore
    private Integer isAdmin;
    @Schema(description = "语言")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String language;

    @Schema(description = "种类集")
    @JsonIgnore
    private List<String> categoryList;

    @Schema(description = "表后缀")
    private String suffixTable;

    /**
     * 随机取出20条数据集
     * @return
     */
    public Map<String, Object> randomLimitMap(String limit) {
        Map<String, Object> randLimitMap = new HashMap<>();
        randLimitMap.put("dataScope", "ORDER BY RAND() LIMIT " + limit);
        return randLimitMap;
    }

    /**
     * 设置排序字段
     * @return
     */
    public Map<String, Object> settingOrderBy(String column) {
        Map<String, Object> orderByMap = new HashMap<>();
        orderByMap.put("dataScope", "ORDER BY " + column);
        return orderByMap;
    }
}

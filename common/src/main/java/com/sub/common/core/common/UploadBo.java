package com.sub.common.core.common;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "上传公共参数")
public class UploadBo implements Serializable {
    @Schema(description = "id")
    private String id;
    @Schema(description = "附加数据")
    private String attach;
}

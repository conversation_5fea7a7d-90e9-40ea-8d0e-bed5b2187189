package com.sub.common.core.common;


import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "查询字段, 返回总数, 如: 查询种类 返回每个种类的数目")
public class FieldCountPo implements Serializable {
    @Schema(description = "名称")
    private String name;
    @Schema(description = "数目")
    private Integer total;

}

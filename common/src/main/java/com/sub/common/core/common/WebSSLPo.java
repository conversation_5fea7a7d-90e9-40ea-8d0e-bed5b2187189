package com.sub.common.core.common;


import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "站点证书信息")
public class WebSSLPo extends BaseEntity implements Serializable {
    @Schema(description = "证书类型: 1: 服务器站点   2: CDN  3:其他  [用于支付自动化申请证书]")
    private Integer type;
    @Schema(description = "域名解析厂商")
    private String dnsIssuer;


    @Schema(description = "证书结束时间")
    private Long noAfter;
    @Schema(description = "申请证书时间")
    private Long notBefore;
    @Schema(description = "证书绑定的域名")
    private String bindDomainList;
    @Schema(description = "加密方式")
    private String sigAlgName;
    @Schema(description = "证书提供者")
    private String issuer;
    @Schema(description = "证书剩余天数")
    private Long dayRemaining;
}

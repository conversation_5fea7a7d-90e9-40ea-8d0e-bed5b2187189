package com.sub.common.core.common;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "基础筛选类")
public class BaeFilter implements Serializable {
    @Schema(description = "基础搜索字符")
    private String searchValue;
    @Schema(description = "排序: 0: asc 1: desc")
    private Integer sort;
    @Schema(description = "本次操作描述")
    private String remark;
    @Schema(description = "排序字段")
    private String sortColumn;
    @Schema(description = "本次请求多少条数据")
    private Integer count;
    @Schema(description = "从第几条开始")
    private Integer page;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "公共基础筛选外加上时间的筛选")
    public static class TimeFilter extends BaeFilter implements Serializable {
        @Schema(description = "开始时间")
        private String filterStartTime;
        @Schema(description = "结束时间")
        private String filterEndTime;
    }
}

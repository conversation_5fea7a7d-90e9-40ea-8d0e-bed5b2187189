package com.sub.common.core.common;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Tree 基类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "Tree 基础类")
public class TreeEntity<T> implements Serializable {
    @Schema(description = "父菜单名称")
    private String parentName;
    private Long parentId;
    @Schema(description = "显示顺序")
    private Integer orderNum;
    @Schema(description = "祖级列表")
    private String ancestors;
    @Schema(description = "子级")
    private List<T> children = new ArrayList<>();
}

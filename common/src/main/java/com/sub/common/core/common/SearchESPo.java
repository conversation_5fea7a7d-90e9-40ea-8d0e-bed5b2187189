package com.sub.common.core.common;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "搜索引擎")
public class SearchESPo implements Serializable {
    @Schema(description = "标题")
    private String title;
    @Schema(description = "链接")
    private String link;
    @Schema(description = "描述")
    private String desc;
    @Schema(description = "图片链接")
    private String image;
}

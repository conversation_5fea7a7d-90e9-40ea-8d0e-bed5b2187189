package com.sub.common.core.common;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "http请求返回对象")
public class HttpResultBo implements Serializable {
    @Schema(description = "状态码")
    private Integer code;
    @Schema(description = "返回信息")
    private String message;
    @Schema(description = "返回数据")
    private String data;
    @Schema(description = "本次请求的链接")
    private String url;
    @Schema(description = "请求的请求头")
    private Map<String, List<String>> headers;


}

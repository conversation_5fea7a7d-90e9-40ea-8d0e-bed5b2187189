package com.sub.common.core.page;


import com.sub.common.core.text.Convert;
import com.sub.common.utils.ServletUtils;
import com.sub.common.utils.StringUtils;

import java.util.concurrent.ThreadLocalRandom;

/**
 * 表格数据处理
 */
public class TableSupportBo {
    /**
     * 当前记录起始索引
     */
    public static final String PAGE_NUM = "pageNum";

    /**
     * 每页显示记录数
     */
    public static final String PAGE_SIZE = "pageSize";

    /**
     * 排序列
     */
    public static final String ORDER_BY_COLUMN = "orderByColumn";

    /**
     * 排序的方向 "desc" 或者 "asc".
     */
    public static final String IS_ASC = "isAsc";

    /**
     * 分页参数合理化
     */
    public static final String REASONABLE = "reasonable";


    /**
     * 限制最大获取数据量
     * @return
     */
    public static Integer limitPageSize() {
        Integer pageSize = Convert.toInt(ServletUtils.getParameter(PAGE_SIZE), 40);
        if (pageSize > 100) {
            pageSize = 40;
        }
        return pageSize;
    }

    private static final String[] ORDERS = {"desc", "asc"};

    public static String randISAsc() {
        int randomIndex = ThreadLocalRandom.current().nextInt(ORDERS.length);
        return ORDERS[randomIndex];
    }

    /**
     * 封装分页对象
     */
    public static PageBo setPageBo() {
        PageBo pageBo = new PageBo();
        pageBo.setPageNum(Convert.toInt(ServletUtils.getParameter(PAGE_NUM), 1));
        pageBo.setPageSize(limitPageSize());
        pageBo.setOrderByColumn(ServletUtils.getParameter(ORDER_BY_COLUMN));
        pageBo.setIsAsc(ServletUtils.getParameter(IS_ASC));
        pageBo.setReasonable(ServletUtils.getParameterToBool(REASONABLE));
        return pageBo;
    }

    /**
     * 封装随机推荐 分页对象
     */
    public static PageBo setRecommendPageBo(String column) {
        PageBo pageBo = new PageBo();
        pageBo.setPageNum(Convert.toInt(ServletUtils.getParameter(PAGE_NUM), 1));
        pageBo.setPageSize(limitPageSize());
        if (StringUtils.isNotEmpty(ServletUtils.getParameter(ORDER_BY_COLUMN))) {
            column = ServletUtils.getParameter(ORDER_BY_COLUMN);
        }
        pageBo.setOrderByColumn(column);
        pageBo.setIsAsc(randISAsc());
        pageBo.setReasonable(ServletUtils.getParameterToBool(REASONABLE));
        return pageBo;
    }


}

package com.sub.common.core.common;



import java.io.Serializable;
import java.util.List;

import com.sub.common.utils.TimeUtils;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "缓存数据封装")
public class CacheRecordBo<T> implements Serializable {
    @Schema(description = "获取数据的时间搓")
    private Long getTime;
    @Schema(description = "name")
    private String name;
    @Schema(description = "图标")
    private String img;
    @Schema(description = "附加数据")
    private String attach;
    @Schema(description = "据上次更新时间")
    private String distanceTime;
    @Schema(description = "数据集")
    private List<T> dataList;



    public CacheRecordBo(String name, List datList) {
        this.name = name;
        this.dataList = datList;
    }

    /**
     * 计算距离上次更新相差多少时间
     */
    public static List<CacheRecordBo> setDistanceTimeList(List<CacheRecordBo> list) {
        for (CacheRecordBo cacheRecordBo : list) {
            setDistanceTimeBo(cacheRecordBo);
        }
        return list;
    }
    public static CacheRecordBo setDistanceTimeBo(CacheRecordBo cacheRecordBo) {
        cacheRecordBo.setDistanceTime(TimeUtils.getTimeBetween(cacheRecordBo.getGetTime(), TimeUtils.getCurrentTime()));
        return cacheRecordBo;
    }

}

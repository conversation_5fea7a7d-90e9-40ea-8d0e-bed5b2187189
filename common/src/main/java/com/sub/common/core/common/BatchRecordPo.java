package com.sub.common.core.common;

import java.io.Serializable;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "批量处理数据")
public class BatchRecordPo<T> implements Serializable {
    @Schema(description = "现在时间戳")
    private Long currentTime;
    @Schema(description = "表名")
    private String daySuffix;
    @Schema(description = "单数据")
    private T data;
    @Schema(description = "list 多数据")
    private List<T> dataList;

    public BatchRecordPo(String daySuffix, List<T> dataList) {
        this.daySuffix = daySuffix;
        this.dataList = dataList;
    }
    public BatchRecordPo(Long currentTime, T data) {
        this.currentTime = currentTime;
        this.data = data;
    }
    public BatchRecordPo(List<T> dataList){
        this.dataList = dataList;
    }
    public BatchRecordPo(String daySuffix, T data) {
        this.daySuffix = daySuffix;
        this.data = data;
    }
    public BatchRecordPo(T data) {
        this.data = data;
    }
}

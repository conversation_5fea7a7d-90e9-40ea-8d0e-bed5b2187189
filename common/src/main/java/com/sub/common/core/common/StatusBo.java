package com.sub.common.core.common;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "基础数据状态")
public class StatusBo implements Serializable {
    @Schema(description = "id")
    private String id;
    @Schema(description = "状态")
    private Integer status;
}

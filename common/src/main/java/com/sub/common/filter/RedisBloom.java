package com.sub.common.filter;

// 注释掉RedisBloom类，因为项目暂时不需要使用Redisson
// @Component
public class RedisBloom {

    // @Autowired
    // private RedissonClient redissonClient;

    /*
     * 创建一个布隆过滤器 redis
     */
    /*
    public <T> RBloomFilter<T> createBloomFilter(String filterName, double errRate, long initSize) {
        RBloomFilter<T> bloomFilter = redissonClient.getBloomFilter(filterName);
        bloomFilter.tryInit(initSize, errRate);
        return bloomFilter;
    }

    /**
     * 判断布隆过滤器是否存在
     */
    /*
    public boolean checkBloomFilter(String filterName) {
        return redissonClient.getBloomFilter(filterName).isExists();
    }

    /**
     * 删除布隆过滤器
     * @param filterName
     */
    /*
    public void deleteBloomFilter(String filterName) {
        redissonClient.getBloomFilter(filterName).delete();
    }

    /**
     * 向布隆过滤器中添加元素
     */
    /*
    public void addToBloomFilter(String filterName, Object objects) {
        RBloomFilter bloomFilter = redissonClient.getBloomFilter(filterName);
        bloomFilter.add(objects);
    }

    /**
     * 向布隆过滤器中添加集合元素
     */
    /*
    public void addToBloomFilter(String filterName, Collection collection) {
        RBloomFilter bloomFilter = redissonClient.getBloomFilter(filterName);
        bloomFilter.add(collection);
    }

    /**
     * 判断一个元素是否存在于布隆过滤器中
     */
    /*
    public boolean containsInBloomFilter(String filterName, Object objects) {
        RBloomFilter bloomFilter = redissonClient.getBloomFilter(filterName);
        return bloomFilter.contains(objects);
    }
    */

}

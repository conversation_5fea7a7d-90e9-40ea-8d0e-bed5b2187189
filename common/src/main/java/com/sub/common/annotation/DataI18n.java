package com.sub.common.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据国际化注解
 * 用于标记需要进行数据国际化处理的Controller方法
 * 
 * 使用示例：
 * @DataI18n
 * @GetMapping("/regions")
 * public CommonResult<List<RegionPo>> getRegions() {
 *     return ok(regionService.getAllRegions());
 * }
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataI18n {
    
    /**
     * 是否启用数据国际化
     * @return true表示启用，false表示禁用
     */
    boolean value() default true;
    
    /**
     * 指定需要国际化的数据类型
     * 如果为空，则自动检测所有支持的类型
     * @return 数据类型数组
     */
    Class<?>[] types() default {};
}

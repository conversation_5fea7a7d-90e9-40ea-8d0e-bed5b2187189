package com.sub.common.annotation;


import java.lang.annotation.*;

/**
 * 通知预警
 */
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Notify {
    /**
     * 通知主题: 必填项
     */
    String subject();

    /**
     * 本次key 后缀: 必填项
     */
    String key();

    /**
     * 提醒最大容忍数, 默认错误 5 次后则开启预警 通知
     * @return
     */
    int maxCount() default 5;

    /**
     * 通知的方式
     */
    int[] method();
}

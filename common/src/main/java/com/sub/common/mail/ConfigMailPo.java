package com.sub.common.mail;


import java.io.Serializable;

import com.sub.common.utils.auth.AuthUtils;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 *  邮箱基础配置, 对应 setting_rule 表中  send_email_config
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "邮箱设置的基础配置")
public class ConfigMailPo implements Serializable {
    @Schema(description = "账号")
    private String account;
    @Schema(description = "主机")
    private String host;
    @Schema(description = "密码")
    private String password;
    @Schema(description = "端口")
    private Integer port;
    @Schema(description = "使用的协议")
    private String protocol;
    @Schema(description = "邮件别名")
    private String personal;
    @Schema(description = "名称")
    private String name;
    @Schema(description = "当前状态")
    private Integer status;


    // 解密
    public static ConfigMailPo decodeEmailConfig(ConfigMailPo configMailPo) {
        configMailPo.setHost(AuthUtils.aesDecrypt(configMailPo.getHost()));
        configMailPo.setAccount(AuthUtils.aesDecrypt(configMailPo.getAccount()));
        configMailPo.setPassword(AuthUtils.aesDecrypt(configMailPo.getPassword()));
        return configMailPo;
    }
    // 加密
    public static ConfigMailPo encryptEmailConfig(ConfigMailPo configMailPo) {
        configMailPo.setHost(AuthUtils.aesEncrypt(configMailPo.getHost()));
        configMailPo.setAccount(AuthUtils.aesEncrypt(configMailPo.getAccount()));
        configMailPo.setPassword(AuthUtils.aesEncrypt(configMailPo.getPassword()));
        return configMailPo;
    }

}

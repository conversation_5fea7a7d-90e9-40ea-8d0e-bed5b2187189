package com.sub.common.mail;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "发件邮箱基础配置")
public class SendMailBo implements Serializable {
    @Schema(description = "接受方账号")
    private String account;
    @Schema(description = "发件主题")
    private String subject;
    @Schema(description = "redis键值")
    private String redisKey;
    @Schema(description = "redis值")
    private String redisValue;
    @Schema(description = "发件时的项目")
    private String project;
    @Schema(description = "是否是验证邮件 [0: 不是 1:是] ")
    private boolean isVerifyEmail;
    @Schema(description = "发件内容")
    private String text;
    @Schema(description = "发件创建时间")
    private Long createTime;
    @Schema(description = "发件状态")
    private Integer status;
}

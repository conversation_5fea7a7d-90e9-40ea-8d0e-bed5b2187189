package com.sub.common.enums;




import com.sub.common.utils.StringUtils;

public enum TimeEnum {
    YEAR(1, "year", "%Y"),             // 年
    QUARTER(2, "quarter", ""),         // 季
    MONTH(3,"month", "%Y-%m"),         // 月
    WEEK(4, "week", ""),               // 周
    DAY(5, "date", "%Y-%m-%d"),        // 天
    HOUR(6,"hour", "%Y-%m-%d %H"),     // 小时
    MINUTE(7,"minute", "%Y-%m-%d %H:%i"), // 分钟
    SECOND(8,"second", "%Y-%m-%d %H:%i:%s") // 秒
    ;

    private Integer value;
    private String timeType;
    private String sqlFormat;
    TimeEnum(Integer value, String timeType, String sqlFormat) {
        this.value = value;
        this.timeType = timeType;
        this.sqlFormat = sqlFormat;
    }

    public Integer getValue() {
        return value;
    }
    public String getTimeType() {
        return timeType;
    }
    public String getSqlFormat() {
        return sqlFormat;
    }


    // 根据所给字符
    public static TimeEnum getTimeTypeEnum(String timeType){
        if (!StringUtils.isEmpty(timeType)) {
            for (TimeEnum timeEnum : TimeEnum.values()) {
                if (timeEnum.getTimeType().equals(timeType)) {
                    return timeEnum;
                }
            }
        }
        return TimeEnum.MONTH;
    }
}

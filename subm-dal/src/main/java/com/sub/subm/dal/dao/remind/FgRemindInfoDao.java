package com.sub.subm.dal.dao.remind;

import java.math.BigDecimal;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.sub.common.core.common.BatchRecordPo;
import com.sub.subm.dal.entity.generate.remind.FgRemindInfoPo;

public interface FgRemindInfoDao {

    BigDecimal totalAmount(FgRemindInfoPo fgRemindInfoPo);

    List<FgRemindInfoPo> filterFgRemindInfoList(FgRemindInfoPo fgRemindInfoPo);

    int insertFgRemindInfo(@Param("record") BatchRecordPo<FgRemindInfoPo> fgRemindInfoPo);

    int updateFgRemindInfo(FgRemindInfoPo fgRemindInfoPo);

    int deleteFgRemindInfo(@Param("arrayId") List<String> arrayIds, @Param("userId") String userId);

    int deleteFgRemindInfoByProductName(@Param("productName") String productName, @Param("userId") String userId);

    int updateFgRemindInfoRate(@Param("rate") BigDecimal rate, @Param("userId") String userId, @Param("id") String id);
}

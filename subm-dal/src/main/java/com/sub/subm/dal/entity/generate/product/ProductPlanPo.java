package com.sub.subm.dal.entity.generate.product;

import java.io.Serializable;
import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sub.common.core.common.BaseEntity;
import com.sub.common.utils.serializer.BigDecimalSerializer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "产品, 地区, 公用一部分数据")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductPlanPo extends BaseEntity implements Serializable  {
    @Schema(description = "产品id")
    private String productId;
    @Schema(description = "产品名称")
    private String productName;
    @Schema(description = "周期, [1: 月 2年]")
    private Integer cycle;
    @Schema(description = "套餐限制人数")
    private Integer vacancy;
    @Schema(description = "描述")
    private String describeId;
    @Schema(description = "产品官网 使用的货币")
    private String currencyId;

    @Schema(description = "平台规定的金额")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal amount;
    @Schema(description = "产品官网价格")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal originalAmount;
    @Schema(description = "平台年付价格")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal yearAmount;
    @Schema(description = "官网年付价格")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal yearOriginalAmount;
    @Schema(description = "拼团价格")
    private BigDecimal bookingAmount;


    @Schema(description = "地区id")
    private String regionId;
    @Schema(description = "免费天数")
    private Integer freeDay;
    @Schema(description = "套餐url")
    private String planUrl;


    @Schema(description = "地区信息")
    @JsonInclude(value=JsonInclude.Include.NON_NULL)
    private RegionPo region;

    @Schema(description = "地区名称")
    private String regionName;
}

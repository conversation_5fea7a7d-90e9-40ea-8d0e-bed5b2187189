package com.sub.subm.dal.dao.config;




import com.sub.subm.dal.entity.generate.config.SettingRulePo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SettingRuleDao {
    /**
     * 获取所有系统规则
     * @return
     */
    List<SettingRulePo> filterSettingRuleList(SettingRulePo settingRulePo);

    /**
     *  获取指定名称的系统规则
     * @param name
     * @return
     */
    SettingRulePo getAppointNameSettingRule(@Param("name") String name);

    /**
     * 修改系统规则
     * @param settingRulePo
     * @return
     */
    int updateSettingRule(SettingRulePo settingRulePo);

}

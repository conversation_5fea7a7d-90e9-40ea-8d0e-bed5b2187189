package com.sub.subm.dal.entity.generate.product;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "地区")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RegionPo implements Serializable {

    @Schema(description = "唯一编号id")
    private Integer id;

    @Schema(description = "不同国家使用的币种")
    private Integer currencyId;

    @Schema(description = "国家名称")
    private String nameCn;

    @Schema(description = "地区英文名")
    private String name;

    @Schema(description = "各地区电话区域码")
    private String areaCode;

    @Schema(description = "国家代码")
    private String countryCode;

    @Schema(description = "国家icon uri")
    private String iconUrl;

    @Schema(description = "创建者")
    private String creator;

    @Schema(description = "创建时间")
    private Long createTime;

    @Schema(description = "最近更新者")
    private String updator;

    @Schema(description = "更新时间")
    private Long updateTime;

    @Schema(description = "热度(数值越高,排名越前)")
    private Integer popular;

    @Schema(description = "是否显示(0:不显示, 1显示)")
    private Integer enable;

    @Schema(description = "货币代码")
    private String currenyCode;
}

package com.sub.subm.dal.entity.generate.basic;


import com.sub.common.core.common.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "种类")
public class CategoryPo extends BaseEntity{
    @Schema(description = "图标")
    private String img;
    @Schema(description = "父分类")
    private String parentId;
    @Schema(description = "英文名称")
    private String name;
    @Schema(description = "中文")
    private String nameCn;
    @Schema(description = "种类下有多少订阅源")
    private Integer countAsset;
}

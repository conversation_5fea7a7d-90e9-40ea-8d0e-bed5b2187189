package com.sub.subm.dal.entity.generate.user;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户三方登录")
public class UserAuthPo implements Serializable {
    @Schema(description = "id")
    private String id;
    @Schema(description = "名称")
    private String name;
    @Schema(description = "账号")
    private String account;
    @Schema(description = "uuid")
    private String uuid;
    @Schema(description = "渠道")
    private String channel;
    @Schema(description = "头像")
    private String avatar;
    @Schema(description = "创建时间")
    private Long createTime;
    @Schema(description = "用户id")
    private String userId;
    @Schema(description = "状态")
    private Integer status;
}

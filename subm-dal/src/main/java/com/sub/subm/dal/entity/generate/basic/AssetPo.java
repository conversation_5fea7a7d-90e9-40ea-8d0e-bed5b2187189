package com.sub.subm.dal.entity.generate.basic;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnore;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "种类关联表")
public class AssetPo implements Serializable {
    @Schema(description = "来源id ")
    private String sourceId;
    @Schema(description = "种类id")
    private String categoryId;

    @Schema(description = "表名")
    @JsonIgnore
    private String tableName;
}

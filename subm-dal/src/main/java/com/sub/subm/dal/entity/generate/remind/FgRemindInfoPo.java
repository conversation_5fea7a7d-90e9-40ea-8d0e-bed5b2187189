package com.sub.subm.dal.entity.generate.remind;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sub.common.utils.serializer.BigDecimalSerializer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "提醒信息")
public class FgRemindInfoPo implements Serializable {
    @Schema(description = "id")
    private String id;
    @Schema(description = "产品名称")
    private String productName;
    @Schema(description = "套餐名称")
    private String planName;
    @Schema(description = "地区名称")
    private String regionName;
    @Schema(description = "金额")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal amount;
    @Schema(description = "货币")
    private String currency;
    @Schema(description = "种类")
    private String category;
    @Schema(description = "创建时间")
    private Long createTime;
    @Schema(description = "图片")
    private String img;
    @Schema(description = "用户id")
    private String userId;
    @Schema(description = "周期")
    private Integer cycles;

    @Schema(description = "汇率")
    private BigDecimal rate;

    @Schema(description = "开始时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long startTime;
    @Schema(description = "结束时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long endTime;

    @Schema(description = "关联的id")
    private String remindId;

    @Schema(description = "月分组")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String date;

    @Schema(description = "分组")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Map<String, Object> params;

    public FgRemindInfoPo(FgRemindPo fgRemindPo, Long createTime) {
        this.remindId = fgRemindPo.getId();
        this.productName = fgRemindPo.getProductName();
        this.planName = fgRemindPo.getPlanName();
        this.regionName = fgRemindPo.getPlanName();
        this.amount = fgRemindPo.getAmount();
        this.currency = fgRemindPo.getCurrency();
        this.category = fgRemindPo.getCategory();
        this.cycles = fgRemindPo.getRemindCycle();
        this.userId = fgRemindPo.getUserId();
        this.createTime = createTime;
        this.rate = fgRemindPo.getRate();
        this.img = fgRemindPo.getImg();
    }
}

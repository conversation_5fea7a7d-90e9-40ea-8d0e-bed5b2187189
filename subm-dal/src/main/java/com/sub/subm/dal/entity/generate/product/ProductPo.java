package com.sub.subm.dal.entity.generate.product;

import java.io.Serializable;
import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "产品")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductPo implements Serializable {

    @Schema(description = "唯一编号id")
    private Integer id;

    @Schema(description = "产品名称")
    private String name;

    @Schema(description = "产品类型")
    private Integer category;

    @Schema(description = "产品图标uri")
    private String iconUrl;

    @Schema(description = "产品参考的url")
    private String productUrl;

    @Schema(description = "原价")
    private BigDecimal originalPrice;

    @Schema(description = "平均价格")
    private BigDecimal averagePrice;

    @Schema(description = "等级[1: 极差 2: 差 3: 一般 4: 好 5:满意]")
    private Integer grade;

    @Schema(description = "共享次数")
    private Integer shareCount;

    @Schema(description = "热度值")
    private Integer popular;

    @Schema(description = "是否在store  1: 是 0不是")
    private Byte isStore;

    @Schema(description = "创建的用户")
    private String creator;

    @Schema(description = "创建时间")
    private Long createTime;

    @Schema(description = "更新者")
    private String updator;

    @Schema(description = "更新时间")
    private Long updateTime;

    @Schema(description = "是否显示  1:显示 0 不显示")
    private Integer enable;

    @Schema(description = "备注")
    private String remark;
}

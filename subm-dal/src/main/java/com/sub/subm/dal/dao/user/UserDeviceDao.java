package com.sub.subm.dal.dao.user;

import com.sub.subm.dal.entity.generate.UserDevicePo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserDeviceDao {

    /**
     * 根据分组hash查询用户设备信息
     */
    UserDevicePo getByHashUserDevice(@Param("groupHash") String groupHash);

    /**
     * 根据设备id查询用户设备信息
     */
    UserDevicePo getByUserDevice(@Param("deviceId") String deviceId);

    /**
     * 根据id查询用户设备信息
     */
    UserDevicePo getByIdUserDevice(@Param("id") String id);

    /**
     * 筛选出 用户设备信息
     */
    List<UserDevicePo> filterUserDevice(UserDevicePo userDevicePo);

    /**
     * 新增用户设备信息
     */
    int insertUserDevice(UserDevicePo userDevicePo);

    /**
     * 修改用户设备信息
     */
    int updateUserDevice(UserDevicePo userDevicePo);
}

package com.sub.subm.dal.entity.bo;


import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "登录错误 实体类")
public class LoginErrBo implements Serializable {
    @Schema(description = "错误次数")
    private Integer count;
    @Schema(description = "错误时间类型")
    private Integer timeEnum;
    @Schema(description = "错误持续时间")
    private Integer duration;
}

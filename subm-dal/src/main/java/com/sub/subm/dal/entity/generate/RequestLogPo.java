package com.sub.subm.dal.entity.generate;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description =  "请求日志")
public class RequestLogPo implements Serializable {
    @Schema(description = "包名")
    private String pageName;
    @Schema(description = "软件版本")
    private String appVersion;
    @Schema(description = "设备号")
    private String deviceId;
    @Schema(description = "设备信息")
    private String deviceModel;
    @Schema(description = "使用软件型号")
    private String osAppVersion;
    @Schema(description = "请求此次的语言")
    private Integer language;
    @Schema(description = "请求类型 [0 : login, 1: ADMIN, 2: WEBHOOK, 3.API]")
    private Integer requestType;
    @Schema(description = "请求此次的用户")
    private String userId;
    @Schema(description = "token 信息")
    private String token;
    @Schema(description = "请求方式")
    private String requestMethod;
    @Schema(description = "请求uri地址")
    private String uriPath;
    @Schema(description = "请求uri 说明")
    private String uriExplain;
    @Schema(description = "请求参数")
    private String requestParam;
    @Schema(description = "此次请求状态  [0: 失败 1:成功]")
    private Integer status;
    @Schema(description = "错误说明")
    private String message;
    @Schema(description = "请求地址")
    private String requestIp;
    @Schema(description = "请求时间")
    private Long createTime;
    @Schema(description = "请求耗时")
    private Long timeConsuming;
    @Schema(description = "服务名称")
    private String serviceName;



    public enum RequestEnum {
        NO_AUTH,
        AUTH,
        API,
        WEBHOOK;
    }
}

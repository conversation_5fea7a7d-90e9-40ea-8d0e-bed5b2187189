package com.sub.subm.dal.dao.product;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.sub.subm.dal.entity.generate.product.CurrencyPo;

public interface CurrencyDao {

    CurrencyPo getCurrencyName(@Param("name") String name);

    CurrencyPo getCurrencyById(@Param("id") Integer id);

    List<CurrencyPo> filterCurrencyList();

    int updateCurrencyList(CurrencyPo currencyPo);
}

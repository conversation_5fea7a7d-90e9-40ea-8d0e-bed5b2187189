package com.sub.subm.dal.entity.bo;

import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "FG 各个货币统计")
public class FgStatPo implements Serializable {
    @Schema(description = "货币")
    private String currency;
    @Schema(description = "总金额")
    private BigDecimal sumAmount;
}

package com.sub.subm.dal.service;



import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sub.common.constant.CacheConstants;
import com.sub.common.core.redis.RedisCache;
import com.sub.common.exception.BaseException;
import com.sub.common.utils.StringUtils;
import com.sub.common.utils.ip.NetworkUtil;
import com.sub.subm.dal.constant.SettingResultConstant;
import com.sub.subm.dal.dao.config.SettingRuleDao;
import com.sub.subm.dal.entity.generate.config.SettingRulePo;

@Service
public class SettingResultServiceImpl {
    private Logger log = LoggerFactory.getLogger(SettingResultServiceImpl.class);

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private SettingRuleDao settingRuleDao;

    /**
     * 初始化设置系统规则
     */
    public void initSettingResult() {
        List<SettingRulePo> list = getSysSettingRuleList(new SettingRulePo());
        Map<String, SettingRulePo> settingMap = new HashMap<>();
        if (list != null && list.size() > 0) {
            for (SettingRulePo resultPo : list) {
                if (resultPo != null) {
                    settingMap.put(resultPo.getName(), resultPo);
                }
            }
            redisCache.setCacheMap(CacheConstants.SETTING_KEY, settingMap);
        }
    }

    /**
     * 获取所有的系统配置
     */
    public List<SettingRulePo> getSysSettingRuleList(SettingRulePo settingRulePo) {
        return settingRuleDao.filterSettingRuleList(settingRulePo);
    }

    /**
     * 修改系统规则
     * @param settingRulePo
     */
    public void updateSettingRule(SettingRulePo settingRulePo) {
        settingRuleDao.updateSettingRule(settingRulePo);
        redisCache.deleteCacheMapValue(CacheConstants.SETTING_KEY, settingRulePo.getName());
    }


    /**
     * 获取指定名称的规则
     * @param name
     * @return
     */
    public SettingRulePo getAppointName(String name) {
       SettingRulePo settingRulePo = redisCache.getCacheMapValue(CacheConstants.SETTING_KEY, name);
       if (settingRulePo == null) {
           settingRulePo = settingRuleDao.getAppointNameSettingRule(name);
           if (settingRulePo != null) {
               redisCache.setCacheMapValue(CacheConstants.SETTING_KEY, name, settingRulePo);
           }
       }
       return settingRulePo;
    }


    /**
     * 获取指定名称的规则 的详细 value 值
     * @param name
     * @return
     */
    public String getAppointNameStr(String name) throws BaseException {
        SettingRulePo settingRulePo = getAppointName(name);
        if (settingRulePo == null || StringUtils.isEmpty(settingRulePo.getValue())) {
            throw new BaseException("param.exception");
        }
        return settingRulePo.getValue();
    }

    /**
     * 判断ip 是否在黑名单中
     * @param ip
     * @throws BaseException
     */
    public void isBlackIPList(String ip) throws BaseException {
        String blackStr = getAppointNameStr(SettingResultConstant.BACK_IP_LIST);
        if (NetworkUtil.isMatchedIp(blackStr, ip)) {
            log.warn("{} ip地址在黑名单中 {}", ip, blackStr);
            throw new BaseException();
        }
    }




}

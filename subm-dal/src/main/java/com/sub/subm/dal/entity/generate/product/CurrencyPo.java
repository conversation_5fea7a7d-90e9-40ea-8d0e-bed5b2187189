package com.sub.subm.dal.entity.generate.product;

import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "货币")
public class CurrencyPo implements Serializable {
    @Schema(description = "id")
    private Integer id;
    @Schema(description = "货币名称")
    private String name;
    @Schema(description = "货币符号")
    private String symbol;
    @Schema(description = "对美汇率")
    private BigDecimal exchangeRate;
    @Schema(description = "简写")
    private String simple;
    @Schema(description = "创建时间")
    private Long createTime;
    @Schema(description = "创建者")
    private String creator;
    @Schema(description = "修改时间")
    private Long updateTime;
    @Schema(description = "修改者")
    private String updator;
    @Schema(description = "是否可用")
    private Integer enable;
}

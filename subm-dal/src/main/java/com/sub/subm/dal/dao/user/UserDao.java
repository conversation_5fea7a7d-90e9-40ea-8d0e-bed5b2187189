package com.sub.subm.dal.dao.user;

import com.sub.subm.dal.entity.generate.user.UserPo;
import org.apache.ibatis.annotations.Param;


import java.util.List;

public interface UserDao {

    UserPo getByIdUser(@Param("id") String id);

    UserPo userLogin(@Param("account") String account,
                     @Param("different") Integer different);

    List<UserPo> filterUser(UserPo userPo);

    int insertUser(UserPo userPo);

    int updateUser(UserPo userPo);




}

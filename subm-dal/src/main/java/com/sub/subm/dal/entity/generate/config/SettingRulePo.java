package com.sub.subm.dal.entity.generate.config;


import java.io.Serializable;

import com.sub.common.core.common.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "系统规则基本字段对象")
public class SettingRulePo extends BaseEntity implements Serializable {
    @Schema(description = "规则对应的图标")
    private String icon;
    @Schema(description = "规则对应的值")
    private String value;
    @Schema(description = "规则的对应的描述")
    private String describe;
    @Schema(description = "区分类型")
    private Integer type;
    @Schema(description = "附加类名")
    private String attachClass;
}

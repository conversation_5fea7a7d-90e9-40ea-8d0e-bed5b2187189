package com.sub.subm.dal.entity.generate.user;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "用户反馈")
public class UserFeedsBackPo implements Serializable {
    @Schema(description = "id")
    private String id;
    @Schema(description = "用户id")
    private String userId;
    @Schema(description = "反馈状态, 不用填")
    private Integer status;
    @Schema(description = "反馈内容")
    private String content;
    @Schema(description = "联系邮箱")
    private String contact;
    @Schema(description = "创建时间")
    private Long createTime;
}

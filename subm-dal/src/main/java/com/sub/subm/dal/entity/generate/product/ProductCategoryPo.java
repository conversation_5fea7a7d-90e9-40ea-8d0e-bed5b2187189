package com.sub.subm.dal.entity.generate.product;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sub.common.core.common.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "种类")
public class ProductCategoryPo extends BaseEntity implements Serializable {

    @Schema(description = "中文名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String nameCn;
    @Schema(description = "热门种类")
    private Integer producer;
    @Schema(description = "图标")
    private String iconUrl;
}

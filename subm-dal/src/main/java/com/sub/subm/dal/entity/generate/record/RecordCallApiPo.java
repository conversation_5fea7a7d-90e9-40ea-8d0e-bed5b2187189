package com.sub.subm.dal.entity.generate.record;


import java.io.Serializable;

import com.sub.common.core.common.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "api 调用记录数据表")
public class RecordCallApiPo extends BaseEntity implements Serializable {
    @Schema(description = "当前日期")
    private Long statTime;
    @Schema(description = "类型")
    private String type;
    @Schema(description = "关联api 的id")
    private String apiUrl;
    @Schema(description = "使用的token")
    private String token;
    @Schema(description = "请求ip地址")
    private String ip;
    @Schema(description = "请求方法")
    private String method;
    @Schema(description = "耗时")
    private Long costTime;
    @Schema(description = "请求状态")
    private Integer status;
    @Schema(description = "最终状态说明")
    private String message;
}

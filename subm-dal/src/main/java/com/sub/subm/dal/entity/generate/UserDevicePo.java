package com.sub.subm.dal.entity.generate;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "用户设备")
public class UserDevicePo implements Serializable {
    @Schema(description = "id")
    private String id;
    @Schema(description = "用户id")
    private String userId;
    @Schema(description = "设备id")
    private String deviceId;
    @Schema(description = "系统类型")
    private String osType;
    @Schema(description = "ip地址")
    private String createIp;
    @Schema(description = "ip地址")
    private String ipAddress;
    @Schema(description = "分组hash")
    private String groupHash;
    @Schema(description = "app 版本")
    private String appVersion;
    @Schema(description = "设备型号")
    private String deviceModel;
    @Schema(description = "第一次记录时间")
    private Long createTime;
    @Schema(description = "更新时间")
    private Long updateTime;

    @Schema(description = "系统版本")
    private String osVersion;
    @Schema(description = "是否是游客")
    private Integer isGuest;
}

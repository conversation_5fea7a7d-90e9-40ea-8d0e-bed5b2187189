package com.sub.subm.dal.dao.remind;

import java.math.BigDecimal;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.sub.subm.dal.entity.generate.remind.FgRemindPo;

public interface FgRemindDao {

    BigDecimal filterFgStatList(FgRemindPo fgRemindPo);

    List<FgRemindPo> filterFgRemindList(FgRemindPo fgRemindPo);

    int insertFgRemind(FgRemindPo fgRemindPo);

    int updateFgRemind(FgRemindPo fgRemindPo);

    int deleteFgRemind(@Param("arrayId") List<String> arrayId, @Param("userId") String userId);


    int deleteFgRemindByProductName(@Param("productName") String productName, @Param("userId") String userId);
}

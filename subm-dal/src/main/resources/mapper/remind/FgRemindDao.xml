<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.subm.dal.dao.remind.FgRemindDao">

    <sql id="Base_Column_List">
        user_id, img, product_name, plan_name, region_id, free_num, free_type,
        create_time, start_time, end_time, amount, currency,
        `desc`, status, remind_type, remind_cycle, is_custom, update_time, category, product_id, rate
    </sql>


    <select id="filterFgStatList" parameterType="com.sub.subm.dal.entity.generate.remind.FgRemindPo"
    resultType="java.math.BigDecimal">
        SELECT SUM(amount / IFNULL(rate, 1)) AS sum_amount
        FROM fg_remind
        <where>
            <if test="userId != null">AND user_id = #{userId} </if>
            <if test="regionId != null">AND region_id = #{regionId}</if>
            <if test="status != null">AND status = #{status}</if>
            <if test="remindType != null">AND remind_type = #{remindType}</if>
        </where>
    </select>


    <select id="filterFgRemindList" parameterType="com.sub.subm.dal.entity.generate.remind.FgRemindPo"
            resultType="com.sub.subm.dal.entity.generate.remind.FgRemindPo">
        SELECT id, img, product_name, plan_name, region_id, free_num, free_type,
        create_time, start_time, end_time, amount, currency,
        `desc`, IF(end_time &lt; UNIX_TIMESTAMP(), 0, status) AS status, remind_type, remind_cycle, is_custom, update_time, category, product_id, rate
        FROM fg_remind
        <where>
            <if test="userId != null">AND user_id = #{userId} </if>
            <if test="regionId != null">AND region_id = #{regionId}</if>
            <if test="status != null">AND IF(end_time &lt; UNIX_TIMESTAMP(), 0, status) = #{status}</if>
            <if test="remindType != null">AND remind_type = #{remindType}</if>
        </where>
    </select>

    <insert id="insertFgRemind" parameterType="com.sub.subm.dal.entity.generate.remind.FgRemindPo"
    useGeneratedKeys="true" keyProperty="id">
        INSERT INTO fg_remind
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <include refid="Base_Column_List"></include>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{userId}, #{img}, #{productName}, #{planName},
            #{regionId}, #{freeNum},
            #{freeType},#{createTime},
            #{startTime}, #{endTime},
            #{amount}, #{currency}, #{desc},
            #{status}, #{remindType}, #{remindCycle},
            #{isCustom}, #{createTime}, #{category}, #{productId}, #{rate}
        </trim>
    </insert>

    <update id="updateFgRemind" parameterType="com.sub.subm.dal.entity.generate.remind.FgRemindPo">
        UPDATE fg_remind
        <trim prefix="SET" suffixOverrides=",">
            <if test="img != null">img = #{img},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="planName != null">plan_name = #{planName},</if>
            <if test="regionId != null">region_id = #{regionId},</if>
            <if test="freeNum != null">free_num = #{freeNum},</if>
            <if test="freeType != null">free_type = #{freeType},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="currency != null">currency = #{currency},</if>
            <if test="desc != null">`desc` = #{desc},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remindType != null">remind_type = #{remindType},</if>
            <if test="remindCycle != null">remind_cycle = #{remindCycle},</if>
            <if test="isCustom != null">is_custom = #{isCustom},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="rate != null">rate = #{rate},</if>
        </trim>
        <where>
            AND id = #{id}
            <if test="userId != null">AND user_id = #{userId} </if>

        </where>

    </update>


    <delete id="deleteFgRemind">
        DELETE FROM fg_remind
        WHERE id IN
        <foreach collection="arrayId" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="userId != null">AND user_id = #{userId} </if>
    </delete>

    <delete id="deleteFgRemindByProductName">
        DELETE FROM fg_remind
        WHERE product_name = #{productName} AND user_id = #{userId}
    </delete>

</mapper>

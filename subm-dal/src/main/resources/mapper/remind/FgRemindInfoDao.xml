<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.subm.dal.dao.remind.FgRemindInfoDao">

    <sql id="Base_Column_List">
        product_name, plan_name,
        region_name, amount, currency, category,
        create_time, img, user_id, cycles, rate, remind_id
    </sql>

    <select id="totalAmount" parameterType="com.sub.subm.dal.entity.generate.remind.FgRemindInfoPo"
            resultType="java.math.BigDecimal">
        SELECT SUM(amount / IFNULL(rate, 1))
        FROM fg_remind_info
        <where>
            <if test="productName != null"> AND product_name LIKE  CONCAT("%", #{productName}, "%")</if>
            <if test="userId != null"> AND user_id = #{userId}</if>
            <if test="cycles != null"> AND cycles = #{cycles}</if>
            <if test="currency != null"> AND currency = #{currency}</if>
            <if test="category != null"> AND category = #{category}</if>
            <if test="startTime != null"> AND create_time >= #{startTime}</if>
            <if test="endTime != null"> AND create_time &lt; #{endTime}</if>
        </where>
    </select>


    <select id="filterFgRemindInfoList" parameterType="com.sub.subm.dal.entity.generate.remind.FgRemindInfoPo"
            resultType="com.sub.subm.dal.entity.generate.remind.FgRemindInfoPo">
        <if test="params == null">
            SELECT id, product_name, plan_name, region_name, currency, category,
                   create_time, img, user_id, cycles, amount
            FROM fg_remind_info
        </if>
        <if test="params != null">
            SELECT
            <if test="params.groupBy == 'year'">
                SUM(amount / IFNULL(rate, 1)) AS amount,
                DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y') AS `date`
            </if>
            <if test="params.groupBy == 'month'">
                SUM(amount / IFNULL(rate, 1)) AS amount,
                DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m') AS `date`
            </if>
            <if test="params.groupBy == 'category'">
                category,
                SUM(amount / IFNULL(rate, 1)) AS amount
            </if>
            FROM fg_remind_info
        </if>
        <where>
            <if test="productName != null"> AND product_name LIKE  CONCAT("%", #{productName}, "%")</if>
            <if test="userId != null"> AND user_id = #{userId}</if>
            <if test="cycles != null"> AND cycles = #{cycles}</if>
            <if test="currency != null"> AND currency = #{currency}</if>
            <if test="category != null"> AND category = #{category}</if>
            <if test="startTime != null"> AND create_time >= #{startTime}</if>
            <if test="endTime != null"> AND create_time &lt; #{endTime}</if>
        </where>
        <if test="params != null">
            <if test="params.groupBy == 'year'">
                GROUP BY DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y')
            </if>
            <if test="params.groupBy == 'month'">
                GROUP BY DATE_FORMAT(FROM_UNIXTIME(create_time), '%Y-%m')
            </if>
            <if test="params.groupBy == 'category'">
                GROUP BY category
            </if>
        </if>
    </select>

    <insert id="insertFgRemindInfo" parameterType="com.sub.common.core.common.BatchRecordPo">
        INSERT INTO fg_remind_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <include refid="Base_Column_List"></include>
        </trim>
        VALUES
        <foreach collection="record.dataList" item="item" index="index" separator=",">
            (
            #{item.productName}, #{item.planName},
            #{item.regionName}, #{item.amount}, #{item.currency}, #{item.category},
            #{item.createTime}, #{item.img}, #{item.userId}, #{item.cycles}, #{item.rate}, #{item.remindId}
            )
        </foreach>
    </insert>

    <update id="updateFgRemindInfo" parameterType="com.sub.subm.dal.entity.generate.remind.FgRemindInfoPo">
        UPDATE fg_remind_info
        <set>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="planName != null">plan_name = #{planName},</if>
            <if test="regionName != null">region_name = #{regionName},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="currency != null">currency = #{currency},</if>
            <if test="category != null">category = #{category},</if>
            <if test="rate != null">rate = #{rate},</if>
        </set>
        WHERE user_id = #{userId}
                AND remind_id = #{remindId}
        AND create_time >= UNIX_TIMESTAMP(DATE_FORMAT(NOW(), '%Y-%m-01'))
        AND create_time &lt; UNIX_TIMESTAMP(DATE_FORMAT(NOW() + INTERVAL 1 MONTH, '%Y-%m-01'))
    </update>

    <delete id="deleteFgRemindInfo">
        DELETE FROM fg_remind_info
        WHERE user_id = #{userId}
        <if test="arrayId != null and arrayId.size() > 0">
            AND remind_id IN
            <foreach collection="arrayId" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </delete>

    <delete id="deleteFgRemindInfoByProductName">
        DELETE FROM fg_remind_info
        WHERE user_id = #{userId}
        AND product_name = #{productName}
    </delete>


    <insert id="insertFgRemindInfoByOne" parameterType="com.sub.subm.dal.entity.generate.remind.FgRemindInfoPo">
        INSERT INTO fg_remind_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <include refid="Base_Column_List"></include>
        </trim>
        VALUES
        (
        #{productName}, #{planName},
        #{regionName}, #{amount}, #{currency}, #{category},
        #{createTime}, #{img}, #{userId}, #{cycles}, #{rate}, #{remindId}
        )
    </insert>


    <update id="updateFgRemindInfoRate">
          UPDATE fg_remind_info
          SET rate = #{rate}
          WHERE user_id = #{userId}
          AND id = #{id}
    </update>

</mapper>

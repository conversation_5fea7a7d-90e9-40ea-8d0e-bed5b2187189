<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.subm.dal.dao.product.ProductDao">

    <sql id="Base_Column_List">
        id, `name`, category, icon_url, product_url,
        original_price, average_price, grade, share_count, popular, is_store,
        creator, create_time, updator, update_time, enable, remark
    </sql>

    <select id="getByProductId" resultType="com.sub.subm.dal.entity.generate.product.ProductPo">
        SELECT <include refid="Base_Column_List"></include>
        FROM product
        WHERE id = #{id}
    </select>

    <select id="filterProductList" resultType="com.sub.subm.dal.entity.generate.product.ProductPo">
        SELECT <include refid="Base_Column_List"></include>
        FROM product
    </select>


</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.subm.dal.dao.product.RegionDao">

    <sql id="Base_Column_List">
        id, currency_id, name_cn, `name`, area_code, country_code,
        icon_url, creator, create_time, updator, update_time,
        popular, enable, curreny_code
    </sql>

    <select id="getAllProductRegionList" resultType="com.sub.subm.dal.entity.generate.product.RegionPo">
        SELECT <include refid="Base_Column_List"></include>
        FROM region
        WHERE enable = 1
    </select>


</mapper>

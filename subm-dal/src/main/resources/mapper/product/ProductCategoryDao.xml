<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.subm.dal.dao.product.ProductCategoryDao">

    <sql id="Base_Column_List">
        id, `name`, name_cn, icon_url
    </sql>


    <select id="getAllProductCategoryList" resultType="com.sub.subm.dal.entity.generate.product.ProductCategoryPo">
        SELECT id, <include refid="Base_Column_List"></include>
        FROM product_category
    </select>

</mapper>

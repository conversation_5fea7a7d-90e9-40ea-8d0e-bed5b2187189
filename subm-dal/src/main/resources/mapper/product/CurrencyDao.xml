<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.subm.dal.dao.product.CurrencyDao">

    <sql id="Base_Column_List">
        `name`, symbol, exchange_rate, `simple`,
         create_time, creator, update_time, updator, enable
    </sql>

    <select id="getCurrencyName" resultType="com.sub.subm.dal.entity.generate.product.CurrencyPo">
        SELECT id, <include refid="Base_Column_List"></include>
        FROM setting_currency
        WHERE `simple` = #{name}
    </select>

    <select id="filterCurrencyList"
            resultType="com.sub.subm.dal.entity.generate.product.CurrencyPo">
        SELECT id, <include refid="Base_Column_List"></include>
        FROM setting_currency
    </select>

    <update id="updateCurrencyList" parameterType="com.sub.subm.dal.entity.generate.product.CurrencyPo">
        UPDATE setting_currency
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">`name` = #{name},</if>
            <if test="symbol != null">symbol = #{symbol},</if>
            <if test="exchangeRate != null">exchange_rate = #{exchangeRate},</if>
            <if test="simple != null">`simple` = #{simple},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        WHERE id = #{id}
    </update>

    <select id="getCurrencyById" resultType="com.sub.subm.dal.entity.generate.product.CurrencyPo">
        SELECT id, <include refid="Base_Column_List"></include>
        FROM setting_currency
        WHERE id = #{id}
    </select>

</mapper>

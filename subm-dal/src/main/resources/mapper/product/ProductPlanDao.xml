<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.subm.dal.dao.product.ProductPlanDao">
    <sql id="Base_Column_List">
        product_id, `name`, `cycle`, amount, original_amount, describe_id,
        region_id, vacancy, currency_id, free_day, plan_url, year_amount, booking_amount,
        year_original_amount
    </sql>


    <select id="getProductPlanCurrency" resultType="java.lang.String">
        SELECT currency_id from product_plan group by currency_id
    </select>

    <select id="filterProductPlanList" resultType="com.sub.subm.dal.entity.generate.product.ProductPlanPo">
        SELECT plan.id, plan.name, plan.free_day, plan.amount, plan.region_id, plan.currency_id,
               p.name as product_name, r.name as region_name
        FROM product_plan plan
        LEFT JOIN product p ON plan.product_id = p.id
        LEFT JOIN region r ON plan.region_id = r.id
        <where>
            <if test="productId != null">AND product_id = #{productId} </if>
            <if test="regionId != null">AND region_id = #{regionId}</if>
        </where>
        AND plan.enable = 1
    </select>

</mapper>

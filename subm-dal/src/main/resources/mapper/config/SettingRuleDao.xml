<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.subm.dal.dao.config.SettingRuleDao">
    <sql id="get_basic_column">
        id, icon, `name`, `value`, `describe`, `type`, attach_class,
        update_time, updator
    </sql>


    <select id="filterSettingRuleList" resultType="com.sub.subm.dal.entity.generate.config.SettingRulePo">
        SELECT <include refid="get_basic_column"></include>
        FROM setting_rule
        <where>
            <if test="name != null and name != ''">
                AND `name` LIKE concat('%', #{name}, '%')
            </if>
            <if test="type != null">
                AND `type` = #{type}
            </if>
        </where>
        <if test="params != null">
            ${params.dataScope}
        </if>
    </select>


    <select id="getAppointNameSettingRule" resultType="com.sub.subm.dal.entity.generate.config.SettingRulePo">
        SELECT <include refid="get_basic_column"></include>
        FROM setting_rule
        WHERE `name` = #{name}
    </select>

    <update id="updateSettingRule" parameterType="com.sub.subm.dal.entity.generate.config.SettingRulePo">
        UPDATE setting_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            <if test="icon != null">
                icon = #{icon},
            </if>
            <if test="value != null and value != null">
                `value` = #{value},
            </if>
            <if test="describe != null and describe != ''">
                `describe` = #{describe},
            </if>
            <if test="type != null">
                `type` = #{type},
            </if>
            <if test="attachClass != null">
                attach_class = #{attachClass},
            </if>
            <if test="updator != null">
                updator = #{updator},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime}
            </if>
        </trim>
        WHERE id = #{id}
    </update>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.subm.dal.dao.user.UserDeviceDao">

    <sql id="Base_Column_List">
        user_id, device_id, create_time, update_time, os_type, create_ip,
        ip_address, group_hash, app_version, device_model, is_guest, os_version
    </sql>

    <select id="getByHashUserDevice" resultType="com.sub.subm.dal.entity.generate.UserDevicePo">
        SELECT id, <include refid="Base_Column_List"></include>
        FROM user_device
        WHERE group_hash = #{groupHash}
    </select>

    <select id="getByUserDevice" resultType="com.sub.subm.dal.entity.generate.UserDevicePo">
        SELECT id, <include refid="Base_Column_List"></include>
        FROM user_device
        WHERE device_id = #{deviceId} AND is_guest = 1 ORDER BY create_time DESC LIMIT 1
    </select>

    <select id="getByIdUserDevice"  resultType="com.sub.subm.dal.entity.generate.UserDevicePo">
        SELECT id, <include refid="Base_Column_List"></include>
        FROM user_device
        WHERE id = #{id}
    </select>

    <select id="filterUserDevice"  resultType="com.sub.subm.dal.entity.generate.UserDevicePo">
        SELECT id, <include refid="Base_Column_List"></include>
        FROM user_device
        <where>
            <if test="userId != null">AND user_id = #{userId}</if>
            <if test="deviceId != null">AND device_id = #{deviceId}</if>
            <if test="osType != null">AND os_type = #{osType}</if>
            <if test="appVersion != null">AND app_version = #{appVersion}</if>
            <if test="isGuest != null">AND is_guest = #{isGuest}</if>
            <if test="osVersion != null">AND os_version = #{osVersion}</if>
        </where>
    </select>

    <insert id="insertUserDevice" parameterType="com.sub.subm.dal.dao.user.UserDeviceDao" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <include refid="Base_Column_List"></include>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{userId}, #{deviceId}, #{createTime}, #{updateTime}, #{osType}, #{createIp},
            #{ipAddress}, #{groupHash}, #{appVersion}, #{deviceModel}, #{isGuest}, #{osVersion}
        </trim>
    </insert>

    <update id="updateUserDevice">
        UPDATE user_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="ipAddress != null">ip_address = #{ipAddress},</if>
            <if test="isGuest != null">is_guest = #{isGuest},</if>
            <if test="groupHash != null">group_hash = #{groupHash},</if>
        </trim>
        WHERE id = #{id}
    </update>


</mapper>

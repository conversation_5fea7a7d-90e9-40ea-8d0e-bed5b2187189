<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.subm.dal.dao.user.UserDao">
    <sql id="Basic_Column_List">
        `name`, phone, email, password, salt, avatar, motto,
        google_id, wechat_id, apple_id, create_ip, ip_address,
        role_id, create_time, last_online_time, enable, device_id, currency
    </sql>

    <select id="getByIdUser" resultType="com.sub.subm.dal.entity.generate.user.UserPo">
        SELECT id, <include refid="Basic_Column_List"></include>
        FROM `user`
        WHERE id = #{id}
    </select>

    <select id="userLogin" resultType="com.sub.subm.dal.entity.generate.user.UserPo">
        SELECT id, <include refid="Basic_Column_List"></include>
        FROM `user`
        <where>
            <if test="different == -1">
                AND id = #{account}
            </if>
            <if test="different == 0">
                AND email = #{account}
            </if>
            <if test="different == 1">
                AND phone = #{account}
            </if>
            <if test="different == 2">
                AND google_id = #{account}
            </if>
            <if test="different == 3">
                AND wechat_id = #{account}
            </if>
            <if test="different == 4">
                AND apple_id = #{account}
            </if>
            AND enable = 1
        </where>
    </select>

    <select id="filterUser" parameterType="com.sub.subm.dal.entity.generate.user.UserPo"
            resultType="com.sub.subm.dal.entity.generate.user.UserPo">
        SELECT id, <include refid="Basic_Column_List"></include>
        FROM `user`
        <where>
            <if test="name != null and name != ''">AND `name` LIKE CONCAT('%', #{name}, '%')</if>
            <if test="roleId != null">AND role_id = #{roleId}</if>
            <if test="email != null and email != null">AND email = #{email}</if>
            <if test="enable != null">AND enable = #{enable}</if>
        </where>
        <if test="params != null">
            ${params.dataScope}
        </if>
    </select>



    <insert id="insertUser" parameterType="com.sub.subm.dal.entity.generate.user.UserPo"
            useGeneratedKeys="true" keyProperty="id" >
        INSERT INTO `user`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <include refid="Basic_Column_List"></include>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{name}, #{phone}, #{email}, #{password}, #{salt}, #{avatar}, #{motto},
            #{googleId}, #{wechatId}, #{appleId}, #{createIp}, #{ipAddress},
            #{roleId}, #{createTime}, #{lastOnlineTime}, #{enable}, #{deviceId}, #{currency}
        </trim>
    </insert>

    <update id="updateUser" parameterType="com.sub.subm.dal.entity.generate.user.UserPo">
        UPDATE `user`
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">`name` = #{name},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="email != null and email != ''">email = #{email},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="salt != null and salt != ''">salt = #{salt},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="motto != null">motto = #{motto},</if>
            <if test="googleId != null">google_id = #{googleId},</if>
            <if test="wechatId != null">wechat_id = #{wechatId}, </if>
            <if test="appleId != null">apple_id = #{appleId}, </if>
            <if test="ipAddress != null">ip_address = #{ipAddress},</if>
            <if test="roleId != null">role_id = #{roleId},</if>
            <if test="lastOnlineTime">last_online_time = #{lastOnlineTime},</if>
            <if test="enable != null">enable = #{enable},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deviceId != null">deviceId = #{deviceId},</if>
            <if test="currency != null and currency != ''">currency = #{currency},</if>
        </trim>
        WHERE id = #{id}
    </update>

</mapper>

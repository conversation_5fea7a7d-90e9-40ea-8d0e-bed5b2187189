<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.subm.dal.dao.user.UserAuthDao">
    <sql id="Base_Column_List">
        `name`, account, uuid, channel, avatar, create_time, status
    </sql>

    <select id="getUserAuthPo" resultType="com.sub.subm.dal.entity.generate.user.UserAuthPo">
        SELECT id, <include refid="Base_Column_List"></include>
        FROM user_auth
        WHERE channel = #{channel} AND uuid = #{uuid}
    </select>

    <insert id="insertUserAuthPo" parameterType="com.sub.subm.dal.entity.generate.user.UserAuthPo">
        INSERT INTO user_auth
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <include refid="Base_Column_List"></include>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{name}, #{account}, #{uuid}, #{channel}, #{avatar}, #{createTime}, #{status}
        </trim>
    </insert>

</mapper>
